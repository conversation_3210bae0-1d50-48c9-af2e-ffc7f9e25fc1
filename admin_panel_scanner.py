#!/usr/bin/env python3
"""
管理面板扫描器 - 专门扫描主站和子域名的管理面板
"""

import requests
import threading
import time
import socket
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import urljoin
import re

class AdminPanelScanner:
    def __init__(self, target_domain):
        self.target_domain = target_domain
        self.session = requests.Session()
        self.session.verify = False
        self.session.timeout = 10
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def check_subdomain(self, subdomain):
        """检查子域名是否存在"""
        try:
            socket.gethostbyname(subdomain)
            return True
        except socket.gaierror:
            return False
    
    def scan_common_subdomains(self):
        """扫描常见子域名"""
        print("[+] 扫描常见子域名...")
        
        common_subdomains = [
            'www', 'admin', 'administrator', 'manage', 'management', 'panel', 'control',
            'cp', 'cpanel', 'webmail', 'mail', 'ftp', 'ssh', 'secure', 'login',
            'dashboard', 'backend', 'api', 'app', 'mobile', 'dev', 'test', 'staging',
            'demo', 'beta', 'alpha', 'support', 'help', 'docs', 'blog', 'news',
            'shop', 'store', 'pay', 'payment', 'billing', 'account', 'user', 'users',
            'member', 'members', 'client', 'clients', 'customer', 'customers',
            'portal', 'gateway', 'proxy', 'cdn', 'static', 'assets', 'media',
            'upload', 'download', 'file', 'files', 'backup', 'db', 'database',
            'phpmyadmin', 'mysql', 'sql', 'adminer', 'pma', 'myadmin'
        ]
        
        active_subdomains = []
        
        for subdomain in common_subdomains:
            full_domain = f"{subdomain}.{self.target_domain}"
            if self.check_subdomain(full_domain):
                print(f"[!] 发现活跃子域名: {full_domain}")
                active_subdomains.append(full_domain)
        
        return active_subdomains
    
    def scan_admin_paths(self, domain):
        """扫描单个域名的管理面板路径"""
        admin_paths = [
            # 通用管理路径
            '/admin', '/admin/', '/admin.php', '/admin.html',
            '/administrator', '/administrator/', '/administrator.php',
            '/manage', '/manage/', '/manage.php',
            '/management', '/management/', '/management.php',
            '/panel', '/panel/', '/panel.php',
            '/control', '/control/', '/control.php',
            '/backend', '/backend/', '/backend.php',
            '/dashboard', '/dashboard/', '/dashboard.php',
            '/console', '/console/', '/console.php',
            
            # 登录页面
            '/login', '/login/', '/login.php', '/login.html',
            '/signin', '/signin/', '/signin.php',
            '/auth', '/auth/', '/auth.php',
            '/user', '/user/', '/user.php',
            '/account', '/account/', '/account.php',
            
            # 数据库管理
            '/phpmyadmin', '/phpmyadmin/', '/phpMyAdmin', '/phpMyAdmin/',
            '/pma', '/pma/', '/myadmin', '/myadmin/',
            '/adminer', '/adminer/', '/adminer.php',
            '/sql', '/sql/', '/mysql', '/mysql/',
            '/database', '/database/', '/db', '/db/',
            
            # CMS特定路径
            '/wp-admin', '/wp-admin/', '/wp-login.php',
            '/administrator/index.php',  # Joomla
            '/admin/index.php',
            '/user.php', '/admin.php',
            
            # 服务器管理
            '/cpanel', '/cpanel/', '/whm', '/whm/',
            '/plesk', '/plesk/', '/webmin', '/webmin/',
            '/directadmin', '/directadmin/',
            
            # 其他常见路径
            '/system', '/system/', '/config', '/config/',
            '/settings', '/settings/', '/options', '/options/',
            '/tools', '/tools/', '/utilities', '/utilities/',
            '/backup', '/backup/', '/restore', '/restore/',
            '/upload', '/upload/', '/file', '/file/',
            '/filemanager', '/filemanager/',
        ]
        
        found_panels = []
        
        for path in admin_paths:
            for protocol in ['http', 'https']:
                try:
                    url = f"{protocol}://{domain}{path}"
                    response = self.session.get(url, allow_redirects=True)
                    
                    if response.status_code == 200:
                        content = response.text.lower()
                        title = self.extract_title(response.text)
                        
                        # 检查管理面板指标
                        admin_indicators = [
                            'login', 'password', 'username', 'admin', 'dashboard',
                            'control panel', 'management', 'administrator',
                            'phpmyadmin', 'cpanel', 'plesk', 'webmin', 'adminer',
                            'sign in', 'log in', 'authentication', 'auth',
                            'welcome to', 'admin panel', 'administration',
                            'backend', 'console', 'manager'
                        ]
                        
                        # 检查表单元素
                        form_indicators = [
                            '<input', 'type="password"', 'type="text"',
                            '<form', 'method="post"', 'action='
                        ]
                        
                        has_admin_content = any(indicator in content for indicator in admin_indicators)
                        has_form = any(indicator in content for indicator in form_indicators)
                        
                        if has_admin_content or has_form:
                            panel_info = {
                                'url': url,
                                'status_code': response.status_code,
                                'title': title,
                                'size': len(response.text),
                                'has_login_form': has_form,
                                'admin_score': sum(1 for indicator in admin_indicators if indicator in content)
                            }
                            
                            found_panels.append(panel_info)
                            print(f"[!] 发现管理面板: {url}")
                            print(f"    标题: {title}")
                            print(f"    管理评分: {panel_info['admin_score']}")
                            print(f"    有登录表单: {has_form}")
                            
                            # 如果找到HTTPS版本，跳过HTTP
                            if protocol == 'https':
                                break
                    
                    elif response.status_code in [301, 302, 303, 307, 308]:
                        # 处理重定向
                        redirect_url = response.headers.get('Location', '')
                        if redirect_url:
                            print(f"[+] 重定向发现: {url} -> {redirect_url}")
                            
                except requests.exceptions.SSLError:
                    # HTTPS连接失败，尝试HTTP
                    if protocol == 'https':
                        continue
                except Exception as e:
                    pass  # 忽略其他错误
        
        return found_panels
    
    def extract_title(self, html_content):
        """提取HTML标题"""
        try:
            title_match = re.search(r'<title>(.*?)</title>', html_content, re.IGNORECASE | re.DOTALL)
            if title_match:
                title = title_match.group(1).strip()
                # 清理标题
                title = re.sub(r'\s+', ' ', title)
                return title[:100]  # 限制长度
        except:
            pass
        return "未知标题"
    
    def check_common_files(self, domain):
        """检查常见的敏感文件"""
        print(f"[+] 检查 {domain} 的敏感文件...")
        
        sensitive_files = [
            '/robots.txt',
            '/sitemap.xml',
            '/.htaccess',
            '/web.config',
            '/config.php',
            '/configuration.php',
            '/settings.php',
            '/database.php',
            '/db.php',
            '/conn.php',
            '/connect.php',
            '/.env',
            '/phpinfo.php',
            '/info.php',
            '/test.php',
            '/readme.txt',
            '/README.md',
            '/changelog.txt',
            '/version.txt',
            '/backup.sql',
            '/dump.sql',
            '/install.php',
            '/setup.php',
        ]
        
        found_files = []
        
        for file_path in sensitive_files:
            for protocol in ['http', 'https']:
                try:
                    url = f"{protocol}://{domain}{file_path}"
                    response = self.session.get(url)
                    
                    if response.status_code == 200 and len(response.text) > 10:
                        print(f"[!] 发现敏感文件: {url}")
                        print(f"    大小: {len(response.text)} 字节")
                        
                        found_files.append({
                            'url': url,
                            'size': len(response.text),
                            'content_preview': response.text[:200]
                        })
                        
                        if protocol == 'https':
                            break
                            
                except Exception as e:
                    pass
        
        return found_files
    
    def run_comprehensive_scan(self):
        """运行全面扫描"""
        print(f"[+] 开始全面扫描: {self.target_domain}")
        print("="*80)
        
        # 1. 扫描子域名
        subdomains = self.scan_common_subdomains()
        
        # 2. 添加主域名到扫描列表
        all_domains = [self.target_domain, f"www.{self.target_domain}"] + subdomains
        all_domains = list(set(all_domains))  # 去重
        
        # 3. 扫描每个域名的管理面板
        all_panels = []
        all_files = []
        
        for domain in all_domains:
            print(f"\n[+] 扫描域名: {domain}")
            panels = self.scan_admin_paths(domain)
            files = self.check_common_files(domain)
            
            all_panels.extend(panels)
            all_files.extend(files)
        
        # 4. 生成报告
        print("\n" + "="*80)
        print("管理面板扫描报告")
        print("="*80)
        
        print(f"\n[+] 发现的活跃域名 ({len(all_domains)} 个):")
        for domain in sorted(all_domains):
            print(f"  - {domain}")
        
        print(f"\n[+] 发现的管理面板 ({len(all_panels)} 个):")
        # 按管理评分排序
        sorted_panels = sorted(all_panels, key=lambda x: x['admin_score'], reverse=True)
        for panel in sorted_panels:
            print(f"  - {panel['url']}")
            print(f"    标题: {panel['title']}")
            print(f"    评分: {panel['admin_score']}, 有表单: {panel['has_login_form']}")
        
        print(f"\n[+] 发现的敏感文件 ({len(all_files)} 个):")
        for file_info in all_files:
            print(f"  - {file_info['url']} ({file_info['size']} 字节)")
        
        return {
            'domains': all_domains,
            'admin_panels': sorted_panels,
            'sensitive_files': all_files
        }

if __name__ == "__main__":
    scanner = AdminPanelScanner("lanmaoba.com")
    results = scanner.run_comprehensive_scan()
