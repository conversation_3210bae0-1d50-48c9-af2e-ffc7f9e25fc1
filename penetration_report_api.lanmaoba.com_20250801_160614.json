{"target": "api.lanmaoba.com", "scan_time": "2025-08-01T16:06:14.483535", "total_findings": 10, "severity_summary": {"high": 2, "medium": 6, "low": 2}, "findings": [{"severity": "HIGH", "title": "路径遍历漏洞 (Directory Traversal)", "description": "API端点存在路径遍历漏洞，攻击者可能能够访问服务器上的敏感文件", "evidence": "测试用例 {'getgonggao': '1', 'keytype': '../../../etc/passwd'} 返回了异常响应长度336字节", "recommendation": "1. 对用户输入进行严格过滤和验证\n2. 使用白名单方式限制可访问的文件\n3. 实施适当的访问控制", "timestamp": "2025-08-01T16:06:14.483535"}, {"severity": "MEDIUM", "title": "敏感信息泄露", "description": "API响应包含加密数据，但可能存在信息泄露风险", "evidence": "API返回大量加密数据，响应长度2604字节，可能包含敏感信息", "recommendation": "1. 减少API响应中的敏感信息\n2. 实施适当的数据脱敏\n3. 加强访问控制和认证", "timestamp": "2025-08-01T16:06:14.483535"}, {"severity": "MEDIUM", "title": "非标准端口暴露", "description": "发现多个非标准端口开放，增加攻击面", "evidence": "开放端口: 21(FTP), 22(SSH), 80(HTTP), 443(HTTPS), 888, 9502, 9520, 9521, 9527", "recommendation": "1. 关闭不必要的端口\n2. 使用防火墙限制访问\n3. 定期审查开放的服务", "timestamp": "2025-08-01T16:06:14.483535"}, {"severity": "MEDIUM", "title": "未识别服务 (端口9527)", "description": "端口9527运行未知服务，返回固定的Base64编码响应", "evidence": "端口9527对所有请求都返回相同响应: 'AfwmxoFEH3s6PuO7b2PfzA=='", "recommendation": "1. 识别并文档化所有运行的服务\n2. 如果服务不必要，考虑关闭\n3. 确保服务有适当的安全配置", "timestamp": "2025-08-01T16:06:14.483535"}, {"severity": "LOW", "title": "FTP服务暴露", "description": "FTP服务对外开放，可能存在暴力破解风险", "evidence": "端口21开放FTP服务: Pure-FTPd [privsep] [TLS]", "recommendation": "1. 如果不需要，关闭FTP服务\n2. 使用强密码策略\n3. 实施登录失败锁定机制\n4. 考虑使用SFTP替代FTP", "timestamp": "2025-08-01T16:06:14.483535"}, {"severity": "HIGH", "title": "路径遍历漏洞 (Directory Traversal)", "description": "API端点存在路径遍历漏洞，攻击者可能能够访问服务器上的敏感文件", "evidence": "测试用例 {'getgonggao': '1', 'keytype': '../../../etc/passwd'} 返回了异常响应长度336字节", "recommendation": "1. 对用户输入进行严格过滤和验证\n2. 使用白名单方式限制可访问的文件\n3. 实施适当的访问控制", "timestamp": "2025-08-01T16:06:14.484538"}, {"severity": "MEDIUM", "title": "敏感信息泄露", "description": "API响应包含加密数据，但可能存在信息泄露风险", "evidence": "API返回大量加密数据，响应长度2604字节，可能包含敏感信息", "recommendation": "1. 减少API响应中的敏感信息\n2. 实施适当的数据脱敏\n3. 加强访问控制和认证", "timestamp": "2025-08-01T16:06:14.484538"}, {"severity": "MEDIUM", "title": "非标准端口暴露", "description": "发现多个非标准端口开放，增加攻击面", "evidence": "开放端口: 21(FTP), 22(SSH), 80(HTTP), 443(HTTPS), 888, 9502, 9520, 9521, 9527", "recommendation": "1. 关闭不必要的端口\n2. 使用防火墙限制访问\n3. 定期审查开放的服务", "timestamp": "2025-08-01T16:06:14.484538"}, {"severity": "MEDIUM", "title": "未识别服务 (端口9527)", "description": "端口9527运行未知服务，返回固定的Base64编码响应", "evidence": "端口9527对所有请求都返回相同响应: 'AfwmxoFEH3s6PuO7b2PfzA=='", "recommendation": "1. 识别并文档化所有运行的服务\n2. 如果服务不必要，考虑关闭\n3. 确保服务有适当的安全配置", "timestamp": "2025-08-01T16:06:14.484538"}, {"severity": "LOW", "title": "FTP服务暴露", "description": "FTP服务对外开放，可能存在暴力破解风险", "evidence": "端口21开放FTP服务: Pure-FTPd [privsep] [TLS]", "recommendation": "1. 如果不需要，关闭FTP服务\n2. 使用强密码策略\n3. 实施登录失败锁定机制\n4. 考虑使用SFTP替代FTP", "timestamp": "2025-08-01T16:06:14.484538"}]}