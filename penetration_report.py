#!/usr/bin/env python3
"""
渗透测试报告生成器
基于发现的漏洞生成详细的安全评估报告
"""

import json
import datetime
import requests
import socket

class PenetrationReport:
    def __init__(self, target_host):
        self.target_host = target_host
        self.findings = []
        self.scan_time = datetime.datetime.now()
        
    def add_finding(self, severity, title, description, evidence, recommendation):
        """添加发现的漏洞"""
        finding = {
            'severity': severity,
            'title': title,
            'description': description,
            'evidence': evidence,
            'recommendation': recommendation,
            'timestamp': datetime.datetime.now().isoformat()
        }
        self.findings.append(finding)
    
    def analyze_discovered_vulnerabilities(self):
        """分析发现的漏洞"""
        
        # 1. 路径遍历漏洞
        self.add_finding(
            severity="HIGH",
            title="路径遍历漏洞 (Directory Traversal)",
            description="API端点存在路径遍历漏洞，攻击者可能能够访问服务器上的敏感文件",
            evidence="测试用例 {'getgonggao': '1', 'keytype': '../../../etc/passwd'} 返回了异常响应长度336字节",
            recommendation="1. 对用户输入进行严格过滤和验证\n2. 使用白名单方式限制可访问的文件\n3. 实施适当的访问控制"
        )
        
        # 2. 信息泄露
        self.add_finding(
            severity="MEDIUM",
            title="敏感信息泄露",
            description="API响应包含加密数据，但可能存在信息泄露风险",
            evidence="API返回大量加密数据，响应长度2604字节，可能包含敏感信息",
            recommendation="1. 减少API响应中的敏感信息\n2. 实施适当的数据脱敏\n3. 加强访问控制和认证"
        )
        
        # 3. 端口暴露
        self.add_finding(
            severity="MEDIUM", 
            title="非标准端口暴露",
            description="发现多个非标准端口开放，增加攻击面",
            evidence="开放端口: 21(FTP), 22(SSH), 80(HTTP), 443(HTTPS), 888, 9502, 9520, 9521, 9527",
            recommendation="1. 关闭不必要的端口\n2. 使用防火墙限制访问\n3. 定期审查开放的服务"
        )
        
        # 4. 神秘服务
        self.add_finding(
            severity="MEDIUM",
            title="未识别服务 (端口9527)",
            description="端口9527运行未知服务，返回固定的Base64编码响应",
            evidence="端口9527对所有请求都返回相同响应: 'AfwmxoFEH3s6PuO7b2PfzA=='",
            recommendation="1. 识别并文档化所有运行的服务\n2. 如果服务不必要，考虑关闭\n3. 确保服务有适当的安全配置"
        )
        
        # 5. FTP服务暴露
        self.add_finding(
            severity="LOW",
            title="FTP服务暴露",
            description="FTP服务对外开放，可能存在暴力破解风险",
            evidence="端口21开放FTP服务: Pure-FTPd [privsep] [TLS]",
            recommendation="1. 如果不需要，关闭FTP服务\n2. 使用强密码策略\n3. 实施登录失败锁定机制\n4. 考虑使用SFTP替代FTP"
        )
    
    def generate_html_report(self):
        """生成HTML格式的报告"""
        html_template = """
<!DOCTYPE html>
<html>
<head>
    <title>渗透测试报告 - {target}</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; }}
        .finding {{ margin: 20px 0; padding: 15px; border-left: 4px solid #ccc; }}
        .high {{ border-left-color: #d32f2f; background-color: #ffebee; }}
        .medium {{ border-left-color: #f57c00; background-color: #fff3e0; }}
        .low {{ border-left-color: #388e3c; background-color: #e8f5e8; }}
        .severity {{ font-weight: bold; padding: 2px 8px; border-radius: 3px; color: white; }}
        .severity.high {{ background-color: #d32f2f; }}
        .severity.medium {{ background-color: #f57c00; }}
        .severity.low {{ background-color: #388e3c; }}
        pre {{ background-color: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>渗透测试报告</h1>
        <p><strong>目标:</strong> {target}</p>
        <p><strong>扫描时间:</strong> {scan_time}</p>
        <p><strong>发现漏洞数量:</strong> {total_findings}</p>
    </div>
    
    <h2>执行摘要</h2>
    <p>本次渗透测试针对 {target} 进行了全面的安全评估，发现了 {total_findings} 个安全问题。
    其中包括高危漏洞 {high_count} 个，中危漏洞 {medium_count} 个，低危漏洞 {low_count} 个。</p>
    
    <h2>漏洞详情</h2>
    {findings_html}
    
    <h2>总体建议</h2>
    <ul>
        <li>立即修复高危漏洞，特别是路径遍历漏洞</li>
        <li>加强输入验证和输出编码</li>
        <li>实施最小权限原则</li>
        <li>定期进行安全审计和渗透测试</li>
        <li>建立安全事件响应流程</li>
    </ul>
</body>
</html>
        """
        
        # 统计漏洞数量
        high_count = len([f for f in self.findings if f['severity'] == 'HIGH'])
        medium_count = len([f for f in self.findings if f['severity'] == 'MEDIUM'])
        low_count = len([f for f in self.findings if f['severity'] == 'LOW'])
        
        # 生成漏洞HTML
        findings_html = ""
        for finding in self.findings:
            severity_class = finding['severity'].lower()
            findings_html += f"""
            <div class="finding {severity_class}">
                <h3>{finding['title']} <span class="severity {severity_class}">{finding['severity']}</span></h3>
                <p><strong>描述:</strong> {finding['description']}</p>
                <p><strong>证据:</strong></p>
                <pre>{finding['evidence']}</pre>
                <p><strong>修复建议:</strong></p>
                <pre>{finding['recommendation']}</pre>
            </div>
            """
        
        return html_template.format(
            target=self.target_host,
            scan_time=self.scan_time.strftime("%Y-%m-%d %H:%M:%S"),
            total_findings=len(self.findings),
            high_count=high_count,
            medium_count=medium_count,
            low_count=low_count,
            findings_html=findings_html
        )
    
    def generate_json_report(self):
        """生成JSON格式的报告"""
        report = {
            'target': self.target_host,
            'scan_time': self.scan_time.isoformat(),
            'total_findings': len(self.findings),
            'severity_summary': {
                'high': len([f for f in self.findings if f['severity'] == 'HIGH']),
                'medium': len([f for f in self.findings if f['severity'] == 'MEDIUM']),
                'low': len([f for f in self.findings if f['severity'] == 'LOW'])
            },
            'findings': self.findings
        }
        return json.dumps(report, indent=2, ensure_ascii=False)
    
    def save_reports(self):
        """保存报告文件"""
        # 分析漏洞
        self.analyze_discovered_vulnerabilities()
        
        # 保存HTML报告
        html_report = self.generate_html_report()
        with open(f'penetration_report_{self.target_host}_{self.scan_time.strftime("%Y%m%d_%H%M%S")}.html', 'w', encoding='utf-8') as f:
            f.write(html_report)
        
        # 保存JSON报告
        json_report = self.generate_json_report()
        with open(f'penetration_report_{self.target_host}_{self.scan_time.strftime("%Y%m%d_%H%M%S")}.json', 'w', encoding='utf-8') as f:
            f.write(json_report)
        
        print(f"[+] 报告已保存:")
        print(f"    HTML: penetration_report_{self.target_host}_{self.scan_time.strftime('%Y%m%d_%H%M%S')}.html")
        print(f"    JSON: penetration_report_{self.target_host}_{self.scan_time.strftime('%Y%m%d_%H%M%S')}.json")
    
    def print_summary(self):
        """打印摘要"""
        self.analyze_discovered_vulnerabilities()
        
        print("="*60)
        print(f"渗透测试摘要 - {self.target_host}")
        print("="*60)
        print(f"扫描时间: {self.scan_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"发现漏洞: {len(self.findings)} 个")
        
        severity_counts = {}
        for finding in self.findings:
            severity = finding['severity']
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        for severity, count in severity_counts.items():
            print(f"  {severity}: {count} 个")
        
        print("\n主要发现:")
        for finding in self.findings:
            print(f"  [{finding['severity']}] {finding['title']}")
        
        print("\n建议优先修复高危和中危漏洞!")

if __name__ == "__main__":
    report = PenetrationReport("api.lanmaoba.com")
    report.print_summary()
    report.save_reports()
